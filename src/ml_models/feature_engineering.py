"""
Feature Engineering Pipeline for ML Models
Creates technical indicators and features for LSTM price prediction
"""

import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
from dataclasses import dataclass

from config.settings import config


@dataclass
class FeatureSet:
    """Container for engineered features"""
    features: np.ndarray
    feature_names: List[str]
    target: Optional[np.ndarray] = None
    timestamps: Optional[List[datetime]] = None
    metadata: Optional[Dict[str, Any]] = None


class TechnicalIndicators:
    """Technical indicator calculations using custom implementations"""

    @staticmethod
    def rsi(prices: np.ndarray, period: int = 14) -> np.ndarray:
        """Relative Strength Index"""
        deltas = np.diff(prices)
        gains = np.where(deltas > 0, deltas, 0)
        losses = np.where(deltas < 0, -deltas, 0)

        avg_gains = np.zeros_like(prices)
        avg_losses = np.zeros_like(prices)

        # Calculate initial averages
        if len(gains) >= period:
            avg_gains[period] = np.mean(gains[:period])
            avg_losses[period] = np.mean(losses[:period])

            # Calculate smoothed averages
            for i in range(period + 1, len(prices)):
                avg_gains[i] = (avg_gains[i-1] * (period - 1) + gains[i-1]) / period
                avg_losses[i] = (avg_losses[i-1] * (period - 1) + losses[i-1]) / period

        # Calculate RSI
        rsi = np.zeros_like(prices)
        mask = avg_losses != 0
        rs = np.divide(avg_gains, avg_losses, out=np.zeros_like(avg_gains), where=mask)
        rsi[mask] = 100 - (100 / (1 + rs[mask]))
        rsi[~mask] = 100  # When avg_losses is 0

        return rsi

    @staticmethod
    def macd(prices: np.ndarray, fast: int = 12, slow: int = 26, signal: int = 9) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """MACD indicator"""
        # Calculate EMAs
        ema_fast = TechnicalIndicators._ema(prices, fast)
        ema_slow = TechnicalIndicators._ema(prices, slow)

        # MACD line
        macd_line = ema_fast - ema_slow

        # Signal line
        signal_line = TechnicalIndicators._ema(macd_line, signal)

        # Histogram
        histogram = macd_line - signal_line

        return macd_line, signal_line, histogram

    @staticmethod
    def _ema(prices: np.ndarray, period: int) -> np.ndarray:
        """Exponential Moving Average"""
        ema = np.zeros_like(prices)
        alpha = 2.0 / (period + 1)

        # Initialize with first price
        ema[0] = prices[0]

        # Calculate EMA
        for i in range(1, len(prices)):
            ema[i] = alpha * prices[i] + (1 - alpha) * ema[i-1]

        return ema

    @staticmethod
    def bollinger_bands(prices: np.ndarray, period: int = 20, std: float = 2.0) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """Bollinger Bands"""
        # Calculate moving average
        ma = np.zeros_like(prices)
        rolling_std = np.zeros_like(prices)

        for i in range(period - 1, len(prices)):
            window = prices[i - period + 1:i + 1]
            ma[i] = np.mean(window)
            rolling_std[i] = np.std(window)

        # Calculate bands
        upper_band = ma + (rolling_std * std)
        lower_band = ma - (rolling_std * std)

        return upper_band, ma, lower_band

    @staticmethod
    def stochastic(high: np.ndarray, low: np.ndarray, close: np.ndarray, k_period: int = 14, d_period: int = 3) -> Tuple[np.ndarray, np.ndarray]:
        """Stochastic Oscillator"""
        k_percent = np.zeros_like(close)

        for i in range(k_period - 1, len(close)):
            highest_high = np.max(high[i - k_period + 1:i + 1])
            lowest_low = np.min(low[i - k_period + 1:i + 1])

            if highest_high != lowest_low:
                k_percent[i] = 100 * (close[i] - lowest_low) / (highest_high - lowest_low)
            else:
                k_percent[i] = 50  # Neutral when no range

        # %D is moving average of %K
        d_percent = np.zeros_like(close)
        for i in range(k_period + d_period - 2, len(close)):
            d_percent[i] = np.mean(k_percent[i - d_period + 1:i + 1])

        return k_percent, d_percent

    @staticmethod
    def williams_r(high: np.ndarray, low: np.ndarray, close: np.ndarray, period: int = 14) -> np.ndarray:
        """Williams %R"""
        williams = np.zeros_like(close)

        for i in range(period - 1, len(close)):
            highest_high = np.max(high[i - period + 1:i + 1])
            lowest_low = np.min(low[i - period + 1:i + 1])

            if highest_high != lowest_low:
                williams[i] = -100 * (highest_high - close[i]) / (highest_high - lowest_low)
            else:
                williams[i] = -50  # Neutral when no range

        return williams

    @staticmethod
    def cci(high: np.ndarray, low: np.ndarray, close: np.ndarray, period: int = 14) -> np.ndarray:
        """Commodity Channel Index"""
        typical_price = (high + low + close) / 3
        cci = np.zeros_like(close)

        for i in range(period - 1, len(close)):
            tp_window = typical_price[i - period + 1:i + 1]
            sma = np.mean(tp_window)
            mean_deviation = np.mean(np.abs(tp_window - sma))

            if mean_deviation != 0:
                cci[i] = (typical_price[i] - sma) / (0.015 * mean_deviation)
            else:
                cci[i] = 0

        return cci

    @staticmethod
    def atr(high: np.ndarray, low: np.ndarray, close: np.ndarray, period: int = 14) -> np.ndarray:
        """Average True Range"""
        true_range = np.zeros_like(close)

        for i in range(1, len(close)):
            tr1 = high[i] - low[i]
            tr2 = abs(high[i] - close[i-1])
            tr3 = abs(low[i] - close[i-1])
            true_range[i] = max(tr1, tr2, tr3)

        # First TR is just high - low
        true_range[0] = high[0] - low[0]

        # Calculate ATR using smoothed average
        atr = np.zeros_like(close)
        atr[period-1] = np.mean(true_range[:period])

        for i in range(period, len(close)):
            atr[i] = (atr[i-1] * (period - 1) + true_range[i]) / period

        return atr

    @staticmethod
    def adx(high: np.ndarray, low: np.ndarray, close: np.ndarray, period: int = 14) -> np.ndarray:
        """Average Directional Index (simplified)"""
        # Calculate directional movement
        dm_plus = np.zeros_like(close)
        dm_minus = np.zeros_like(close)

        for i in range(1, len(close)):
            up_move = high[i] - high[i-1]
            down_move = low[i-1] - low[i]

            if up_move > down_move and up_move > 0:
                dm_plus[i] = up_move
            if down_move > up_move and down_move > 0:
                dm_minus[i] = down_move

        # Calculate ATR for normalization
        atr = TechnicalIndicators.atr(high, low, close, period)

        # Calculate DI+ and DI-
        di_plus = np.zeros_like(close)
        di_minus = np.zeros_like(close)

        for i in range(period, len(close)):
            dm_plus_sum = np.sum(dm_plus[i-period+1:i+1])
            dm_minus_sum = np.sum(dm_minus[i-period+1:i+1])

            if atr[i] != 0:
                di_plus[i] = 100 * dm_plus_sum / (period * atr[i])
                di_minus[i] = 100 * dm_minus_sum / (period * atr[i])

        # Calculate ADX
        adx = np.zeros_like(close)
        for i in range(period, len(close)):
            di_sum = di_plus[i] + di_minus[i]
            if di_sum != 0:
                dx = 100 * abs(di_plus[i] - di_minus[i]) / di_sum
                adx[i] = dx

        # Smooth ADX
        for i in range(period + 1, len(close)):
            adx[i] = (adx[i-1] * (period - 1) + adx[i]) / period

        return adx

    @staticmethod
    def obv(close: np.ndarray, volume: np.ndarray) -> np.ndarray:
        """On Balance Volume"""
        obv = np.zeros_like(close)
        obv[0] = volume[0]

        for i in range(1, len(close)):
            if close[i] > close[i-1]:
                obv[i] = obv[i-1] + volume[i]
            elif close[i] < close[i-1]:
                obv[i] = obv[i-1] - volume[i]
            else:
                obv[i] = obv[i-1]

        return obv


class PriceFeatures:
    """Price-based feature engineering"""
    
    @staticmethod
    def price_returns(prices: np.ndarray, periods: List[int] = [1, 5, 15]) -> Dict[str, np.ndarray]:
        """Calculate price returns for different periods"""
        features = {}
        for period in periods:
            returns = np.zeros_like(prices)
            returns[period:] = (prices[period:] - prices[:-period]) / prices[:-period]
            features[f'return_{period}m'] = returns
        return features
    
    @staticmethod
    def price_volatility(prices: np.ndarray, window: int = 20) -> np.ndarray:
        """Rolling price volatility"""
        returns = np.diff(prices) / prices[:-1]
        volatility = np.zeros_like(prices)
        
        for i in range(window, len(prices)):
            volatility[i] = np.std(returns[i-window:i])
        
        return volatility
    
    @staticmethod
    def price_momentum(prices: np.ndarray, periods: List[int] = [5, 10, 20]) -> Dict[str, np.ndarray]:
        """Price momentum indicators"""
        features = {}
        for period in periods:
            momentum = np.zeros_like(prices)
            momentum[period:] = prices[period:] / prices[:-period] - 1
            features[f'momentum_{period}'] = momentum
        return features
    
    @staticmethod
    def support_resistance(prices: np.ndarray, window: int = 20) -> Tuple[np.ndarray, np.ndarray]:
        """Dynamic support and resistance levels"""
        support = np.zeros_like(prices)
        resistance = np.zeros_like(prices)
        
        for i in range(window, len(prices)):
            window_prices = prices[i-window:i]
            support[i] = np.min(window_prices)
            resistance[i] = np.max(window_prices)
        
        return support, resistance


class VolumeFeatures:
    """Volume-based feature engineering"""
    
    @staticmethod
    def volume_profile(prices: np.ndarray, volumes: np.ndarray, bins: int = 20) -> np.ndarray:
        """Volume profile analysis"""
        volume_profile = np.zeros_like(prices)
        
        for i in range(len(prices)):
            if i < bins:
                continue
            
            price_range = prices[i-bins:i]
            volume_range = volumes[i-bins:i]
            
            # Create price bins
            price_min, price_max = np.min(price_range), np.max(price_range)
            if price_max == price_min:
                continue
            
            bin_edges = np.linspace(price_min, price_max, bins + 1)
            bin_indices = np.digitize(price_range, bin_edges) - 1
            bin_indices = np.clip(bin_indices, 0, bins - 1)
            
            # Sum volume for each price bin
            bin_volumes = np.zeros(bins)
            for j, bin_idx in enumerate(bin_indices):
                bin_volumes[bin_idx] += volume_range[j]
            
            # Find the bin with highest volume (Point of Control)
            poc_bin = np.argmax(bin_volumes)
            poc_price = (bin_edges[poc_bin] + bin_edges[poc_bin + 1]) / 2
            
            # Distance from current price to POC
            volume_profile[i] = (prices[i] - poc_price) / poc_price
        
        return volume_profile
    
    @staticmethod
    def volume_indicators(volumes: np.ndarray, periods: List[int] = [5, 10, 20]) -> Dict[str, np.ndarray]:
        """Volume-based indicators"""
        features = {}
        
        for period in periods:
            # Volume moving average
            vol_ma = np.zeros_like(volumes)
            for i in range(period, len(volumes)):
                vol_ma[i] = np.mean(volumes[i-period:i])
            features[f'volume_ma_{period}'] = vol_ma
            
            # Volume ratio
            vol_ratio = np.zeros_like(volumes)
            vol_ratio[period:] = volumes[period:] / vol_ma[period:]
            features[f'volume_ratio_{period}'] = vol_ratio
        
        return features


class MarketMicrostructure:
    """Market microstructure features"""
    
    @staticmethod
    def bid_ask_spread(bid_prices: np.ndarray, ask_prices: np.ndarray) -> np.ndarray:
        """Bid-ask spread analysis"""
        spread = (ask_prices - bid_prices) / ((ask_prices + bid_prices) / 2)
        return spread
    
    @staticmethod
    def order_flow_imbalance(buy_volume: np.ndarray, sell_volume: np.ndarray) -> np.ndarray:
        """Order flow imbalance"""
        total_volume = buy_volume + sell_volume
        imbalance = np.zeros_like(buy_volume)
        
        mask = total_volume > 0
        imbalance[mask] = (buy_volume[mask] - sell_volume[mask]) / total_volume[mask]
        
        return imbalance
    
    @staticmethod
    def liquidity_metrics(volumes: np.ndarray, prices: np.ndarray, window: int = 20) -> Dict[str, np.ndarray]:
        """Liquidity-based metrics"""
        features = {}
        
        # Amihud illiquidity ratio
        illiquidity = np.zeros_like(prices)
        for i in range(1, len(prices)):
            if volumes[i] > 0:
                price_impact = abs(prices[i] - prices[i-1]) / prices[i-1]
                illiquidity[i] = price_impact / volumes[i]
        
        features['illiquidity'] = illiquidity
        
        # Volume-weighted average price deviation
        vwap_dev = np.zeros_like(prices)
        for i in range(window, len(prices)):
            window_prices = prices[i-window:i]
            window_volumes = volumes[i-window:i]
            
            if np.sum(window_volumes) > 0:
                vwap = np.sum(window_prices * window_volumes) / np.sum(window_volumes)
                vwap_dev[i] = (prices[i] - vwap) / vwap
        
        features['vwap_deviation'] = vwap_dev
        
        return features


class FeatureEngineer:
    """Main feature engineering pipeline"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.feature_window = config.FEATURE_WINDOW_SIZE
        self.sequence_length = config.LSTM_SEQUENCE_LENGTH
        
        self.technical_indicators = TechnicalIndicators()
        self.price_features = PriceFeatures()
        self.volume_features = VolumeFeatures()
        self.microstructure = MarketMicrostructure()
    
    def create_features(self, market_data: pd.DataFrame) -> FeatureSet:
        """Create comprehensive feature set from market data"""
        
        if len(market_data) < self.feature_window:
            raise ValueError(f"Insufficient data: need at least {self.feature_window} points")
        
        # Extract OHLCV data
        high = market_data['high'].values
        low = market_data['low'].values
        close = market_data['close'].values
        volume = market_data['volume'].values
        
        feature_dict = {}
        feature_names = []
        
        # Technical indicators
        try:
            # RSI
            rsi = self.technical_indicators.rsi(close)
            feature_dict['rsi'] = rsi
            feature_names.append('rsi')
            
            # MACD
            macd, macd_signal, macd_hist = self.technical_indicators.macd(close)
            feature_dict['macd'] = macd
            feature_dict['macd_signal'] = macd_signal
            feature_dict['macd_histogram'] = macd_hist
            feature_names.extend(['macd', 'macd_signal', 'macd_histogram'])
            
            # Bollinger Bands
            bb_upper, bb_middle, bb_lower = self.technical_indicators.bollinger_bands(close)
            bb_position = (close - bb_lower) / (bb_upper - bb_lower)
            bb_width = (bb_upper - bb_lower) / bb_middle
            
            feature_dict['bb_position'] = bb_position
            feature_dict['bb_width'] = bb_width
            feature_names.extend(['bb_position', 'bb_width'])
            
            # Stochastic
            stoch_k, stoch_d = self.technical_indicators.stochastic(high, low, close)
            feature_dict['stoch_k'] = stoch_k
            feature_dict['stoch_d'] = stoch_d
            feature_names.extend(['stoch_k', 'stoch_d'])
            
            # Williams %R
            williams_r = self.technical_indicators.williams_r(high, low, close)
            feature_dict['williams_r'] = williams_r
            feature_names.append('williams_r')
            
            # CCI
            cci = self.technical_indicators.cci(high, low, close)
            feature_dict['cci'] = cci
            feature_names.append('cci')
            
            # ATR
            atr = self.technical_indicators.atr(high, low, close)
            atr_pct = atr / close
            feature_dict['atr_pct'] = atr_pct
            feature_names.append('atr_pct')
            
            # ADX
            adx = self.technical_indicators.adx(high, low, close)
            feature_dict['adx'] = adx
            feature_names.append('adx')
            
            # OBV
            obv = self.technical_indicators.obv(close, volume)
            obv_normalized = (obv - np.mean(obv)) / np.std(obv)
            feature_dict['obv_normalized'] = obv_normalized
            feature_names.append('obv_normalized')
            
        except Exception as e:
            self.logger.warning(f"Error calculating technical indicators: {e}")
        
        # Price features
        try:
            # Price returns
            returns = self.price_features.price_returns(close)
            for name, values in returns.items():
                feature_dict[name] = values
                feature_names.append(name)
            
            # Price volatility
            volatility = self.price_features.price_volatility(close)
            feature_dict['volatility'] = volatility
            feature_names.append('volatility')
            
            # Price momentum
            momentum = self.price_features.price_momentum(close)
            for name, values in momentum.items():
                feature_dict[name] = values
                feature_names.append(name)
            
            # Support/Resistance
            support, resistance = self.price_features.support_resistance(close)
            support_distance = (close - support) / close
            resistance_distance = (resistance - close) / close
            
            feature_dict['support_distance'] = support_distance
            feature_dict['resistance_distance'] = resistance_distance
            feature_names.extend(['support_distance', 'resistance_distance'])
            
        except Exception as e:
            self.logger.warning(f"Error calculating price features: {e}")
        
        # Volume features
        try:
            # Volume indicators
            vol_indicators = self.volume_features.volume_indicators(volume)
            for name, values in vol_indicators.items():
                feature_dict[name] = values
                feature_names.append(name)
            
            # Volume profile
            vol_profile = self.volume_features.volume_profile(close, volume)
            feature_dict['volume_profile'] = vol_profile
            feature_names.append('volume_profile')
            
        except Exception as e:
            self.logger.warning(f"Error calculating volume features: {e}")
        
        # Combine all features
        feature_matrix = []
        valid_features = []
        
        for name in feature_names:
            if name in feature_dict:
                values = feature_dict[name]
                if not np.all(np.isnan(values)):
                    feature_matrix.append(values)
                    valid_features.append(name)
        
        if not feature_matrix:
            raise ValueError("No valid features generated")
        
        features = np.column_stack(feature_matrix)
        
        # Handle NaN values
        features = np.nan_to_num(features, nan=0.0, posinf=0.0, neginf=0.0)
        
        # Create target (future price change)
        target = None
        if 'target_timeframe' in market_data.columns:
            target = market_data['target_timeframe'].values
        
        return FeatureSet(
            features=features,
            feature_names=valid_features,
            target=target,
            timestamps=market_data.index.tolist() if hasattr(market_data.index, 'tolist') else None,
            metadata={
                'data_length': len(market_data),
                'feature_count': len(valid_features),
                'window_size': self.feature_window
            }
        )
    
    def create_sequences(self, feature_set: FeatureSet) -> Tuple[np.ndarray, Optional[np.ndarray]]:
        """Create sequences for LSTM training"""
        features = feature_set.features
        target = feature_set.target
        
        if len(features) < self.sequence_length:
            raise ValueError(f"Insufficient data for sequences: need at least {self.sequence_length} points")
        
        # Create sequences
        X_sequences = []
        y_sequences = [] if target is not None else None
        
        for i in range(self.sequence_length, len(features)):
            X_sequences.append(features[i-self.sequence_length:i])
            
            if target is not None:
                y_sequences.append(target[i])
        
        X = np.array(X_sequences)
        y = np.array(y_sequences) if y_sequences is not None else None
        
        return X, y
    
    def normalize_features(self, features: np.ndarray, fit_scaler: bool = True) -> Tuple[np.ndarray, Optional[Any]]:
        """Normalize features using robust scaling"""
        from sklearn.preprocessing import RobustScaler
        
        if fit_scaler:
            scaler = RobustScaler()
            normalized = scaler.fit_transform(features)
            return normalized, scaler
        else:
            # Assume scaler is provided separately
            return features, None
    
    def get_feature_importance(self, feature_names: List[str], model_weights: np.ndarray) -> Dict[str, float]:
        """Calculate feature importance from model weights"""
        if len(feature_names) != len(model_weights):
            raise ValueError("Feature names and weights length mismatch")
        
        # Normalize weights to get relative importance
        abs_weights = np.abs(model_weights)
        total_weight = np.sum(abs_weights)
        
        if total_weight == 0:
            return {name: 0.0 for name in feature_names}
        
        importance = abs_weights / total_weight
        
        return dict(zip(feature_names, importance))
