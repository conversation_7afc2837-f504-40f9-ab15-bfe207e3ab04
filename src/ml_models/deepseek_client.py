"""
DeepSeek API Client for Sentiment Analysis and Market Intelligence
Provides AI-powered market sentiment analysis for trading signals
"""

import asyncio
import json
import logging
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
import aiohttp
import time
from dataclasses import dataclass

from config.settings import config


@dataclass
class SentimentAnalysis:
    """Sentiment analysis result"""
    asset: str
    sentiment_score: float  # -1.0 to 1.0
    confidence: float  # 0.0 to 1.0
    reasoning: str
    timestamp: datetime
    market_outlook: str  # "bullish", "bearish", "neutral"
    key_factors: List[str]


@dataclass
class MarketIntelligence:
    """Market intelligence result"""
    asset: str
    prediction_1m: float  # Expected price change %
    prediction_5m: float
    prediction_15m: float
    confidence: float
    risk_level: str  # "low", "medium", "high"
    entry_recommendation: str  # "strong_buy", "buy", "hold", "sell", "strong_sell"
    timestamp: datetime


class DeepSeekClient:
    """DeepSeek API client for AI-powered market analysis"""
    
    def __init__(self):
        self.api_key = config.DEEPSEEK_API_KEY
        self.api_base = config.DEEPSEEK_API_BASE
        self.model = config.DEEPSEEK_MODEL
        self.max_tokens = config.DEEPSEEK_MAX_TOKENS
        self.temperature = config.DEEPSEEK_TEMPERATURE
        
        self.logger = logging.getLogger(__name__)
        self.session: Optional[aiohttp.ClientSession] = None
        
        # Rate limiting
        self.last_request_time = 0
        self.min_request_interval = 1.0  # Minimum seconds between requests
        
        # Cache for recent analyses
        self.sentiment_cache: Dict[str, SentimentAnalysis] = {}
        self.intelligence_cache: Dict[str, MarketIntelligence] = {}
        self.cache_ttl = 300  # 5 minutes
    
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            headers={
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    def _is_cache_valid(self, timestamp: datetime) -> bool:
        """Check if cached data is still valid"""
        return (datetime.now() - timestamp).total_seconds() < self.cache_ttl
    
    async def _rate_limit(self):
        """Implement rate limiting"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.min_request_interval:
            sleep_time = self.min_request_interval - time_since_last
            await asyncio.sleep(sleep_time)
        
        self.last_request_time = time.time()
    
    async def _make_request(self, messages: List[Dict[str, str]]) -> Optional[str]:
        """Make request to DeepSeek API"""
        if not self.session:
            raise RuntimeError("Client not initialized. Use async context manager.")
        
        await self._rate_limit()
        
        payload = {
            "model": self.model,
            "messages": messages,
            "max_tokens": self.max_tokens,
            "temperature": self.temperature,
            "stream": False
        }
        
        try:
            async with self.session.post(f"{self.api_base}/chat/completions", json=payload) as response:
                if response.status == 200:
                    data = await response.json()
                    return data['choices'][0]['message']['content']
                else:
                    error_text = await response.text()
                    self.logger.error(f"DeepSeek API error {response.status}: {error_text}")
                    return None
                    
        except Exception as e:
            self.logger.error(f"DeepSeek API request failed: {e}")
            return None
    
    async def analyze_sentiment(self, asset: str, market_data: Dict[str, Any]) -> Optional[SentimentAnalysis]:
        """Analyze market sentiment for an asset"""
        
        # Check cache first
        cache_key = f"{asset}_sentiment"
        if cache_key in self.sentiment_cache:
            cached = self.sentiment_cache[cache_key]
            if self._is_cache_valid(cached.timestamp):
                return cached
        
        # Prepare market context
        price_change_24h = market_data.get('price_change_24h', 0)
        volume_change = market_data.get('volume_change', 0)
        liquidity = market_data.get('liquidity_usd', 0)
        
        prompt = f"""
        Analyze the market sentiment for {asset} based on the following data:
        
        - 24h Price Change: {price_change_24h:.2f}%
        - Volume Change: {volume_change:.2f}%
        - Liquidity: ${liquidity:,.2f}
        - Current Time: {datetime.now().isoformat()}
        
        Provide a JSON response with:
        {{
            "sentiment_score": <float between -1.0 and 1.0>,
            "confidence": <float between 0.0 and 1.0>,
            "market_outlook": "<bullish/bearish/neutral>",
            "reasoning": "<brief explanation>",
            "key_factors": ["<factor1>", "<factor2>", ...]
        }}
        
        Focus on short-term (1-15 minute) trading opportunities.
        """
        
        messages = [
            {"role": "system", "content": "You are an expert cryptocurrency market analyst specializing in short-term trading signals. Provide precise, actionable sentiment analysis."},
            {"role": "user", "content": prompt}
        ]
        
        response = await self._make_request(messages)
        if not response:
            return None
        
        try:
            # Extract JSON from response
            json_start = response.find('{')
            json_end = response.rfind('}') + 1
            if json_start == -1 or json_end == 0:
                raise ValueError("No JSON found in response")
            
            json_str = response[json_start:json_end]
            data = json.loads(json_str)
            
            sentiment = SentimentAnalysis(
                asset=asset,
                sentiment_score=float(data['sentiment_score']),
                confidence=float(data['confidence']),
                reasoning=data['reasoning'],
                timestamp=datetime.now(),
                market_outlook=data['market_outlook'],
                key_factors=data['key_factors']
            )
            
            # Cache the result
            self.sentiment_cache[cache_key] = sentiment
            
            return sentiment
            
        except Exception as e:
            self.logger.error(f"Failed to parse sentiment analysis response: {e}")
            return None
    
    async def get_market_intelligence(self, asset: str, technical_data: Dict[str, Any]) -> Optional[MarketIntelligence]:
        """Get AI-powered market intelligence and predictions"""
        
        # Check cache first
        cache_key = f"{asset}_intelligence"
        if cache_key in self.intelligence_cache:
            cached = self.intelligence_cache[cache_key]
            if self._is_cache_valid(cached.timestamp):
                return cached
        
        # Prepare technical context
        rsi = technical_data.get('rsi', 50)
        macd = technical_data.get('macd', 0)
        bb_position = technical_data.get('bollinger_position', 0.5)
        volume_ratio = technical_data.get('volume_ratio', 1.0)
        
        prompt = f"""
        Provide market intelligence for {asset} based on technical analysis:
        
        - RSI: {rsi:.2f}
        - MACD: {macd:.4f}
        - Bollinger Band Position: {bb_position:.2f} (0=lower, 0.5=middle, 1=upper)
        - Volume Ratio: {volume_ratio:.2f}x average
        
        Predict price movements for 1m, 5m, and 15m timeframes.
        
        Provide a JSON response with:
        {{
            "prediction_1m": <expected price change % in 1 minute>,
            "prediction_5m": <expected price change % in 5 minutes>,
            "prediction_15m": <expected price change % in 15 minutes>,
            "confidence": <float between 0.0 and 1.0>,
            "risk_level": "<low/medium/high>",
            "entry_recommendation": "<strong_buy/buy/hold/sell/strong_sell>"
        }}
        
        Focus on actionable short-term trading signals.
        """
        
        messages = [
            {"role": "system", "content": "You are an expert quantitative analyst specializing in cryptocurrency micro-trading. Provide precise numerical predictions for short-term price movements."},
            {"role": "user", "content": prompt}
        ]
        
        response = await self._make_request(messages)
        if not response:
            return None
        
        try:
            # Extract JSON from response
            json_start = response.find('{')
            json_end = response.rfind('}') + 1
            if json_start == -1 or json_end == 0:
                raise ValueError("No JSON found in response")
            
            json_str = response[json_start:json_end]
            data = json.loads(json_str)
            
            # Parse predictions (handle percentage strings)
            def parse_prediction(value):
                if isinstance(value, str):
                    # Remove % sign if present
                    value = value.replace('%', '')
                return float(value)

            intelligence = MarketIntelligence(
                asset=asset,
                prediction_1m=parse_prediction(data['prediction_1m']),
                prediction_5m=parse_prediction(data['prediction_5m']),
                prediction_15m=parse_prediction(data['prediction_15m']),
                confidence=float(data['confidence']),
                risk_level=data['risk_level'],
                entry_recommendation=data['entry_recommendation'],
                timestamp=datetime.now()
            )
            
            # Cache the result
            self.intelligence_cache[cache_key] = intelligence
            
            return intelligence
            
        except Exception as e:
            self.logger.error(f"Failed to parse market intelligence response: {e}")
            return None
    
    async def batch_analyze(self, assets: List[str], market_data: Dict[str, Dict[str, Any]]) -> Dict[str, Dict[str, Any]]:
        """Batch analyze multiple assets for efficiency"""
        results = {}
        
        # Process in batches to avoid rate limits
        batch_size = 3
        for i in range(0, len(assets), batch_size):
            batch = assets[i:i + batch_size]
            batch_tasks = []
            
            for asset in batch:
                asset_data = market_data.get(asset, {})
                
                # Create tasks for both sentiment and intelligence
                sentiment_task = self.analyze_sentiment(asset, asset_data)
                intelligence_task = self.get_market_intelligence(asset, asset_data.get('technical', {}))
                
                batch_tasks.extend([sentiment_task, intelligence_task])
            
            # Execute batch
            batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
            
            # Process results
            for j, asset in enumerate(batch):
                sentiment_result = batch_results[j * 2]
                intelligence_result = batch_results[j * 2 + 1]
                
                results[asset] = {
                    'sentiment': sentiment_result if not isinstance(sentiment_result, Exception) else None,
                    'intelligence': intelligence_result if not isinstance(intelligence_result, Exception) else None
                }
        
        return results
    
    def clear_cache(self):
        """Clear all cached data"""
        self.sentiment_cache.clear()
        self.intelligence_cache.clear()
    
    def get_cache_stats(self) -> Dict[str, int]:
        """Get cache statistics"""
        return {
            'sentiment_cache_size': len(self.sentiment_cache),
            'intelligence_cache_size': len(self.intelligence_cache)
        }
