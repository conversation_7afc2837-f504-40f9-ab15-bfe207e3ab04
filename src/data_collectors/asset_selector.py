"""
Asset Selection Engine
Identifies top liquidity pairs and filters assets based on criteria
"""

import asyncio
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
import structlog
from .simple_asset_provider import simple_asset_provider
from .alchemy_market_client import alchemy_market_client
from .price_aggregator import price_aggregator
from .cache_manager import cache_manager, CacheKeys
from config.settings import config

logger = structlog.get_logger(__name__)


class AssetSelector:
    """
    Selects and filters trading assets based on liquidity, volume, and sentiment
    """
    
    def __init__(self):
        self.min_liquidity_usd = config.MIN_LIQUIDITY_USD
        self.min_volume_multiplier = config.MIN_VOLUME_MULTIPLIER
        self.top_assets_count = config.TOP_ASSETS_COUNT
        self.cache_ttl = 300  # 5 minutes cache for asset selection
        
        # Common stablecoins and base tokens on Polygon
        self.base_tokens = {
            'USDC': '******************************************',
            'USDT': '******************************************',
            'DAI': '******************************************',
            'WETH': '******************************************',
            'WMATIC': '******************************************'
        }
        
        # Target token for our signals (MATIC)
        self.target_token = {
            'symbol': 'MATIC',
            'address': '******************************************'  # WMATIC
        }
    
    async def get_top_assets(self, force_refresh: bool = False) -> List[Dict[str, Any]]:
        """Get top trading assets based on liquidity and volume"""
        cache_key = CacheKeys.top_assets()
        
        if not force_refresh:
            cached_assets = await cache_manager.get(cache_key)
            if cached_assets:
                logger.debug("top_assets_cache_hit")
                return cached_assets
        
        try:
            # Collect data from all DEX sources
            all_assets = await self._collect_all_assets()
            
            # Filter and rank assets
            filtered_assets = await self._filter_assets(all_assets)
            
            # Get sentiment data for top assets
            top_assets = await self._enrich_with_sentiment(filtered_assets[:self.top_assets_count * 2])
            
            # Final ranking with sentiment
            final_assets = self._rank_assets_with_sentiment(top_assets)[:self.top_assets_count]
            
            # Cache results
            await cache_manager.set(cache_key, final_assets, self.cache_ttl)
            
            logger.info(
                "top_assets_selected",
                count=len(final_assets),
                total_candidates=len(all_assets)
            )
            
            return final_assets
            
        except Exception as e:
            logger.error("get_top_assets_failed", error=str(e))
            return []
    
    async def _collect_all_assets(self) -> List[Dict[str, Any]]:
        """Collect asset data from all DEX sources"""
        all_assets = []
        
        # Get data from Simple Asset Provider (replaces deprecated subgraphs)
        tasks = [
            self._get_simple_provider_assets(),
            self._get_simple_provider_pools()
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        for result in results:
            if not isinstance(result, Exception) and result:
                all_assets.extend(result)
        
        # Deduplicate by token pair
        unique_assets = {}
        for asset in all_assets:
            key = f"{asset['token0_address']}_{asset['token1_address']}"
            if key not in unique_assets or asset['liquidity_usd'] > unique_assets[key]['liquidity_usd']:
                unique_assets[key] = asset
        
        return list(unique_assets.values())
    
    async def _get_simple_provider_assets(self) -> List[Dict[str, Any]]:
        """Get assets from Simple Asset Provider (QuickSwap replacement)"""
        try:
            pairs = await simple_asset_provider.get_top_pairs(100)
            assets = []

            for pair in pairs:
                asset = {
                    'source': 'simple_provider',
                    'pair_address': pair['id'],
                    'token0_address': pair['token0']['id'],
                    'token0_symbol': pair['token0']['symbol'],
                    'token0_name': pair['token0']['name'],
                    'token1_address': pair['token1']['id'],
                    'token1_symbol': pair['token1']['symbol'],
                    'token1_name': pair['token1']['name'],
                    'liquidity_usd': float(pair['reserveUSD']),
                    'volume_24h_usd': float(pair['volumeUSD']),
                    'tx_count': int(pair['txCount']),
                    'token0_price': float(pair['token0Price']),
                    'token1_price': float(pair['token1Price']),
                    'created_at': int(pair['createdAtTimestamp'])
                }
                assets.append(asset)
            
            return assets
            
        except Exception as e:
            logger.error("quickswap_assets_failed", error=str(e))
            return []
    
    async def _get_simple_provider_pools(self) -> List[Dict[str, Any]]:
        """Get assets from Simple Asset Provider (Uniswap V3 replacement)"""
        try:
            pools = await simple_asset_provider.get_top_pools(100)
            assets = []

            for pool in pools:
                asset = {
                    'source': 'simple_provider_pools',
                    'pair_address': pool['id'],
                    'token0_address': pool['token0']['id'],
                    'token0_symbol': pool['token0']['symbol'],
                    'token0_name': pool['token0']['name'],
                    'token1_address': pool['token1']['id'],
                    'token1_symbol': pool['token1']['symbol'],
                    'token1_name': pool['token1']['name'],
                    'liquidity_usd': float(pool['totalValueLockedUSD']),
                    'volume_24h_usd': float(pool['volumeUSD']),
                    'tx_count': int(pool['txCount']),
                    'token0_price': float(pool['token0Price']),
                    'token1_price': float(pool['token1Price']),
                    'fee_tier': pool['feeTier'],
                    'created_at': int(pool['createdAtTimestamp'])
                }
                assets.append(asset)

            return assets

        except Exception as e:
            logger.error("simple_provider_pools_failed", error=str(e))
            return []
    
    async def _filter_assets(self, assets: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Filter assets based on liquidity and volume criteria"""
        filtered_assets = []
        
        for asset in assets:
            # Check minimum liquidity
            if asset['liquidity_usd'] < self.min_liquidity_usd:
                continue
            
            # Check if pair contains a base token
            token0_symbol = asset['token0_symbol']
            token1_symbol = asset['token1_symbol']
            
            has_base_token = (
                token0_symbol in self.base_tokens or
                token1_symbol in self.base_tokens
            )
            
            if not has_base_token:
                continue
            
            # Calculate volume metrics
            volume_to_liquidity_ratio = asset['volume_24h_usd'] / asset['liquidity_usd'] if asset['liquidity_usd'] > 0 else 0
            
            # Add calculated metrics
            asset['volume_to_liquidity_ratio'] = volume_to_liquidity_ratio
            asset['has_base_token'] = has_base_token
            
            # Determine which token is the trading token (not the base token)
            if token0_symbol in self.base_tokens:
                asset['trading_token'] = {
                    'address': asset['token1_address'],
                    'symbol': asset['token1_symbol'],
                    'name': asset['token1_name']
                }
                asset['base_token'] = {
                    'address': asset['token0_address'],
                    'symbol': asset['token0_symbol'],
                    'name': asset['token0_name']
                }
            else:
                asset['trading_token'] = {
                    'address': asset['token0_address'],
                    'symbol': asset['token0_symbol'],
                    'name': asset['token0_name']
                }
                asset['base_token'] = {
                    'address': asset['token1_address'],
                    'symbol': asset['token1_symbol'],
                    'name': asset['token1_name']
                }
            
            filtered_assets.append(asset)
        
        # Sort by liquidity * volume score
        filtered_assets.sort(
            key=lambda x: x['liquidity_usd'] * x['volume_to_liquidity_ratio'],
            reverse=True
        )
        
        logger.info(
            "assets_filtered",
            total_assets=len(assets),
            filtered_count=len(filtered_assets),
            min_liquidity=self.min_liquidity_usd
        )
        
        return filtered_assets
    
    async def _enrich_with_sentiment(self, assets: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Enrich assets with sentiment data from Alchemy Market Client"""
        enriched_assets = []
        
        # Get sentiment data for unique trading tokens
        unique_tokens = {}
        for asset in assets:
            token_symbol = asset['trading_token']['symbol']
            if token_symbol not in unique_tokens:
                unique_tokens[token_symbol] = []
            unique_tokens[token_symbol].append(asset)
        
        # Fetch sentiment data concurrently
        sentiment_tasks = []
        for token_symbol in unique_tokens.keys():
            sentiment_tasks.append(self._get_token_sentiment(token_symbol))
        
        sentiment_results = await asyncio.gather(*sentiment_tasks, return_exceptions=True)
        
        # Apply sentiment data to assets
        for i, token_symbol in enumerate(unique_tokens.keys()):
            sentiment_data = sentiment_results[i] if not isinstance(sentiment_results[i], Exception) else {}
            
            for asset in unique_tokens[token_symbol]:
                asset['sentiment'] = sentiment_data
                enriched_assets.append(asset)
        
        return enriched_assets
    
    async def _get_token_sentiment(self, token_symbol: str) -> Dict[str, Any]:
        """Get sentiment data for a token"""
        cache_key = CacheKeys.sentiment_data(token_symbol)
        
        # Check cache first
        cached_sentiment = await cache_manager.get(cache_key)
        if cached_sentiment:
            return cached_sentiment
        
        try:
            # Get comprehensive sentiment data from Alchemy Market Client
            if alchemy_market_client:
                sentiment_data = await alchemy_market_client.get_market_sentiment_summary(token_symbol)
            else:
                sentiment_data = {}
            
            # Calculate sentiment score (0-1)
            sentiment_score = self._calculate_sentiment_score(sentiment_data)
            
            result = {
                'score': sentiment_score,
                'data': sentiment_data,
                'timestamp': datetime.now().isoformat()
            }
            
            # Cache for 5 minutes
            await cache_manager.set(cache_key, result, 300)
            
            return result
            
        except Exception as e:
            logger.error("token_sentiment_failed", token_symbol=token_symbol, error=str(e))
            return {
                'score': 0.5,  # Neutral sentiment as fallback
                'data': {},
                'timestamp': datetime.now().isoformat()
            }
    
    def _calculate_sentiment_score(self, sentiment_data: Dict[str, Any]) -> float:
        """Calculate normalized sentiment score from 0 to 1"""
        if not sentiment_data:
            return 0.5  # Neutral
        
        score = 0.5  # Start neutral
        
        # Fear & Greed Index (0-100, higher is more greedy/bullish)
        fear_greed = sentiment_data.get('fear_greed_index', 50)
        score += (fear_greed - 50) / 100 * 0.3  # 30% weight
        
        # Funding rate (negative = bullish, positive = bearish)
        funding_rate = sentiment_data.get('avg_funding_rate', 0)
        score -= funding_rate * 10 * 0.2  # 20% weight
        
        # Long/Short ratio (>1 = more longs, <1 = more shorts)
        ls_ratio = sentiment_data.get('long_short_ratio', 1)
        if ls_ratio > 1:
            score += min((ls_ratio - 1) * 0.2, 0.2)  # 20% weight, capped
        else:
            score -= min((1 - ls_ratio) * 0.2, 0.2)
        
        # Liquidation data (high liquidations can indicate trend reversal)
        liquidation_24h = sentiment_data.get('liquidation_24h', 0)
        if liquidation_24h > 1000000:  # $1M+ liquidations
            score -= 0.1  # Slight negative impact
        
        # Clamp to 0-1 range
        return max(0, min(1, score))
    
    def _rank_assets_with_sentiment(self, assets: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Final ranking incorporating sentiment scores"""
        for asset in assets:
            sentiment_score = asset.get('sentiment', {}).get('score', 0.5)
            
            # Combined score: liquidity * volume * sentiment
            combined_score = (
                asset['liquidity_usd'] * 
                asset['volume_to_liquidity_ratio'] * 
                (sentiment_score + 0.5)  # Ensure sentiment doesn't zero out the score
            )
            
            asset['combined_score'] = combined_score
        
        # Sort by combined score
        assets.sort(key=lambda x: x['combined_score'], reverse=True)
        
        return assets
    
    async def is_asset_suitable_for_trading(self, asset: Dict[str, Any]) -> bool:
        """Check if an asset meets all criteria for trading"""
        # Check basic criteria
        if asset['liquidity_usd'] < self.min_liquidity_usd:
            return False
        
        # Check sentiment threshold
        sentiment_score = asset.get('sentiment', {}).get('score', 0.5)
        if sentiment_score < config.CONFIDENCE_THRESHOLD:
            return False
        
        # Check volume activity
        if asset['volume_to_liquidity_ratio'] < 0.1:  # Very low activity
            return False
        
        return True


# Global asset selector instance
asset_selector = AssetSelector()
