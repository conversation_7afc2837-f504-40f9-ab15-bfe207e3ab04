"""
Alchemy Market Data Client - Replacement for Coinglass
Uses Alchemy's comprehensive APIs for market data and sentiment analysis
"""

import asyncio
import aiohttp
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import structlog
from config.settings import config

logger = structlog.get_logger(__name__)


class AlchemyMarketClient:
    """
    Alchemy Market Data client - replaces Coinglass functionality
    Provides token prices, transfers analysis, and market sentiment
    """
    
    def __init__(self, api_key: Optional[str] = None):
        self.api_key = api_key or config.ALCHEMY_API_KEY
        if not self.api_key:
            raise ValueError("Alchemy API key is required")
            
        self.base_url = f"https://polygon-mainnet.g.alchemy.com/v2/{self.api_key}"
        self.prices_api_url = f"https://api.g.alchemy.com/prices/v1/{self.api_key}"
        self.session = None
        self.rate_limit_delay = 0.2  # 200ms between requests
        self.last_request_time = 0
        
        # Common token addresses on Polygon
        self.token_addresses = {
            'MATIC': '******************************************',  # Native MATIC
            'USDC': '******************************************',
            'USDT': '******************************************',
            'WETH': '******************************************',
            'WBTC': '******************************************'
        }
        
    async def _ensure_session(self):
        """Ensure aiohttp session exists"""
        if self.session is None or self.session.closed:
            headers = {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
            
            timeout = aiohttp.ClientTimeout(total=30)
            self.session = aiohttp.ClientSession(
                headers=headers,
                timeout=timeout
            )
    
    async def _make_request(self, url: str, method: str = "POST", data: Optional[Dict] = None, params: Optional[Dict] = None) -> Dict[str, Any]:
        """Make API request with rate limiting"""
        await self._ensure_session()
        
        # Rate limiting
        current_time = asyncio.get_event_loop().time()
        time_since_last = current_time - self.last_request_time
        if time_since_last < self.rate_limit_delay:
            await asyncio.sleep(self.rate_limit_delay - time_since_last)
        
        try:
            if method == "POST":
                async with self.session.post(url, json=data) as response:
                    self.last_request_time = asyncio.get_event_loop().time()
                    
                    if response.status == 429:
                        await asyncio.sleep(2)
                        return await self._make_request(url, method, data, params)
                    
                    if response.status != 200:
                        raise Exception(f"HTTP {response.status}: {await response.text()}")
                    
                    return await response.json()
            else:
                async with self.session.get(url, params=params) as response:
                    self.last_request_time = asyncio.get_event_loop().time()
                    
                    if response.status == 429:
                        await asyncio.sleep(2)
                        return await self._make_request(url, method, data, params)
                    
                    if response.status != 200:
                        raise Exception(f"HTTP {response.status}: {await response.text()}")
                    
                    return await response.json()
                    
        except Exception as e:
            logger.error(
                "alchemy_market_request_failed",
                url=url,
                method=method,
                error=str(e)
            )
            raise
    
    async def get_token_prices(self, symbols: List[str] = None) -> Dict[str, Any]:
        """Get current token prices from real sources"""
        try:
            if symbols is None:
                symbols = ['MATIC', 'USDC', 'USDT', 'WETH', 'WBTC']

            # Use CoinGecko API for real price data
            prices = []

            # Map symbols to CoinGecko IDs
            coingecko_ids = {
                'MATIC': 'matic-network',
                'USDC': 'usd-coin',
                'USDT': 'tether',
                'WETH': 'ethereum',
                'WBTC': 'wrapped-bitcoin'
            }

            # Build CoinGecko API request
            ids = [coingecko_ids.get(symbol) for symbol in symbols if symbol in coingecko_ids]
            if not ids:
                return {'data': []}

            url = f"https://api.coingecko.com/api/v3/simple/price?ids={','.join(ids)}&vs_currencies=usd"

            async with aiohttp.ClientSession() as session:
                async with session.get(url, timeout=aiohttp.ClientTimeout(total=10)) as response:
                    if response.status == 200:
                        price_data = await response.json()

                        for symbol in symbols:
                            coingecko_id = coingecko_ids.get(symbol)
                            if coingecko_id and coingecko_id in price_data:
                                prices.append({
                                    'symbol': symbol,
                                    'price': price_data[coingecko_id]['usd'],
                                    'address': self.token_addresses.get(symbol, ''),
                                    'timestamp': datetime.now().isoformat()
                                })

            logger.info(
                "alchemy_token_prices_fetched",
                symbols=symbols,
                price_count=len(prices)
            )

            return {'data': prices}
            
        except Exception as e:
            logger.error(
                "alchemy_get_token_prices_failed",
                symbols=symbols,
                error=str(e)
            )
            return {'data': []}
    
    async def get_transfer_activity(self, token_address: str, hours: int = 24) -> Dict[str, Any]:
        """Get transfer activity - replaces funding rates analysis"""
        try:
            # Get recent transfers to analyze activity
            data = {
                "jsonrpc": "2.0",
                "method": "alchemy_getAssetTransfers",
                "params": [{
                    "fromBlock": "latest",
                    "toBlock": "latest",
                    "contractAddresses": [token_address],
                    "category": ["erc20"],
                    "maxCount": "0x64",  # 100 transfers
                    "excludeZeroValue": True
                }],
                "id": 1
            }
            
            response = await self._make_request(self.base_url, data=data)
            transfers = response.get('result', {}).get('transfers', [])
            
            # Analyze transfer patterns
            total_value = 0
            large_transfers = 0
            unique_addresses = set()
            
            for transfer in transfers:
                value = float(transfer.get('value', 0))
                total_value += value
                
                if value > 1000:  # Large transfer threshold
                    large_transfers += 1
                
                unique_addresses.add(transfer.get('from', ''))
                unique_addresses.add(transfer.get('to', ''))
            
            activity_score = min(100, (len(transfers) * 2 + large_transfers * 5) / 10)
            
            result = {
                'total_transfers': len(transfers),
                'total_value': total_value,
                'large_transfers': large_transfers,
                'unique_addresses': len(unique_addresses),
                'activity_score': activity_score,
                'timestamp': datetime.now().isoformat()
            }
            
            logger.info(
                "alchemy_transfer_activity_analyzed",
                token_address=token_address,
                activity_score=activity_score,
                transfers=len(transfers)
            )
            
            return result
            
        except Exception as e:
            logger.error(
                "alchemy_get_transfer_activity_failed",
                token_address=token_address,
                error=str(e)
            )
            return {
                'total_transfers': 0,
                'total_value': 0,
                'large_transfers': 0,
                'unique_addresses': 0,
                'activity_score': 50,  # Neutral score
                'timestamp': datetime.now().isoformat()
            }
    
    async def get_market_sentiment_summary(self, symbol: str = "MATIC") -> Dict[str, Any]:
        """Get comprehensive market sentiment - replaces Coinglass sentiment"""
        try:
            # Get token address
            token_address = self.token_addresses.get(symbol.upper())
            if not token_address:
                logger.warning(f"Unknown token symbol: {symbol}")
                return self._get_neutral_sentiment(symbol)
            
            # Fetch multiple data points concurrently
            tasks = [
                self.get_token_prices([symbol]),
                self.get_transfer_activity(token_address),
                self._get_network_activity()
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            price_data = results[0] if not isinstance(results[0], Exception) else {'data': []}
            transfer_data = results[1] if not isinstance(results[1], Exception) else {}
            network_data = results[2] if not isinstance(results[2], Exception) else {}
            
            # Calculate sentiment score
            sentiment_score = self._calculate_sentiment_score(price_data, transfer_data, network_data)
            
            summary = {
                'symbol': symbol.upper(),
                'timestamp': datetime.now().isoformat(),
                'sentiment_score': sentiment_score,
                'price_data': price_data.get('data', []),
                'transfer_activity': transfer_data.get('activity_score', 50),
                'network_activity': network_data.get('activity_level', 'moderate'),
                'confidence': min(100, max(0, sentiment_score))
            }
            
            logger.info(
                "alchemy_market_sentiment_calculated",
                symbol=symbol,
                sentiment_score=sentiment_score,
                confidence=summary['confidence']
            )
            
            return summary
            
        except Exception as e:
            logger.error(
                "alchemy_get_market_sentiment_failed",
                symbol=symbol,
                error=str(e)
            )
            return self._get_neutral_sentiment(symbol)
    
    def _calculate_sentiment_score(self, price_data: Dict, transfer_data: Dict, network_data: Dict) -> float:
        """Calculate overall sentiment score from various data points"""
        score = 50.0  # Start neutral
        
        # Price momentum (if available)
        if price_data.get('data'):
            # Assume positive if we have price data
            score += 10
        
        # Transfer activity
        activity_score = transfer_data.get('activity_score', 50)
        score += (activity_score - 50) * 0.3
        
        # Network activity
        network_level = network_data.get('activity_level', 'moderate')
        if network_level == 'high':
            score += 15
        elif network_level == 'low':
            score -= 10
        
        return max(0, min(100, score))
    
    async def _get_network_activity(self) -> Dict[str, Any]:
        """Get real network activity from Polygon RPC"""
        try:
            from .polygon_rpc import polygon_rpc

            # Get real gas prices and block info
            gas_data = await polygon_rpc.get_gas_price()
            block_info = await polygon_rpc.get_latest_block()

            if not gas_data or not block_info:
                # Fallback to basic data if RPC fails
                return {
                    'gas_utilization': 50,
                    'activity_level': 'moderate',
                    'block_number': 0
                }

            # Calculate utilization based on gas prices
            fast_gas = gas_data.get('fast', 30)
            if fast_gas > 50:
                activity_level = 'high'
                utilization = min(90, 50 + (fast_gas - 30) * 2)
            elif fast_gas > 30:
                activity_level = 'moderate'
                utilization = 30 + (fast_gas - 20) * 2
            else:
                activity_level = 'low'
                utilization = max(10, fast_gas)

            return {
                'gas_utilization': utilization,
                'activity_level': activity_level,
                'block_number': block_info.get('number', 0)
            }

        except Exception as e:
            logger.error("alchemy_get_network_activity_failed", error=str(e))
            return {'gas_utilization': 50, 'activity_level': 'moderate', 'block_number': 0}
    
    def _get_neutral_sentiment(self, symbol: str) -> Dict[str, Any]:
        """Return neutral sentiment when data is unavailable"""
        return {
            'symbol': symbol.upper(),
            'timestamp': datetime.now().isoformat(),
            'sentiment_score': 50.0,
            'price_data': [],
            'transfer_activity': 50,
            'network_activity': 'moderate',
            'confidence': 30
        }
    
    async def close(self):
        """Close aiohttp session"""
        if self.session and not self.session.closed:
            await self.session.close()


# Global Alchemy market client instance
alchemy_market_client = None
if config.ALCHEMY_API_KEY:
    alchemy_market_client = AlchemyMarketClient()
