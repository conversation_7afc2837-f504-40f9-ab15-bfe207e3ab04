"""
DEX Data Collectors for QuickSwap and Uniswap V3
Fetches liquidity, volume, and price data from subgraph APIs
"""

import asyncio
import aiohttp
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
import structlog
from config.settings import config

logger = structlog.get_logger(__name__)


class BaseSubgraphCollector:
    """Base class for subgraph data collection"""
    
    def __init__(self, subgraph_url: str, name: str):
        self.subgraph_url = subgraph_url
        self.name = name
        self.session = None
        self.rate_limit_delay = 0.1  # 100ms between requests
        self.last_request_time = 0
        
    async def _ensure_session(self):
        """Ensure aiohttp session exists"""
        if self.session is None or self.session.closed:
            timeout = aiohttp.ClientTimeout(total=30)
            self.session = aiohttp.ClientSession(timeout=timeout)
    
    async def _execute_query(self, query: str, variables: Optional[Dict] = None) -> Dict[str, Any]:
        """Execute GraphQL query with rate limiting"""
        await self._ensure_session()
        
        # Rate limiting
        current_time = asyncio.get_event_loop().time()
        time_since_last = current_time - self.last_request_time
        if time_since_last < self.rate_limit_delay:
            await asyncio.sleep(self.rate_limit_delay - time_since_last)
        
        payload = {
            'query': query,
            'variables': variables or {}
        }
        
        try:
            async with self.session.post(self.subgraph_url, json=payload) as response:
                self.last_request_time = asyncio.get_event_loop().time()
                
                if response.status != 200:
                    raise Exception(f"HTTP {response.status}: {await response.text()}")
                
                data = await response.json()
                
                if 'errors' in data:
                    raise Exception(f"GraphQL errors: {data['errors']}")
                
                return data.get('data', {})
                
        except Exception as e:
            logger.error(
                "subgraph_query_failed",
                subgraph=self.name,
                error=str(e),
                query=query[:100] + "..." if len(query) > 100 else query
            )
            raise
    
    async def close(self):
        """Close aiohttp session"""
        if self.session and not self.session.closed:
            await self.session.close()


class QuickSwapCollector(BaseSubgraphCollector):
    """QuickSwap data collector using The Graph subgraph"""
    
    def __init__(self):
        super().__init__(config.QUICKSWAP_SUBGRAPH_URL, "QuickSwap")
    
    async def get_top_pairs(self, limit: int = 20) -> List[Dict[str, Any]]:
        """Get top trading pairs by volume"""
        query = """
        query GetTopPairs($limit: Int!) {
            pairs(
                first: $limit,
                orderBy: volumeUSD,
                orderDirection: desc,
                where: {
                    volumeUSD_gt: "1000"
                }
            ) {
                id
                token0 {
                    id
                    symbol
                    name
                    decimals
                }
                token1 {
                    id
                    symbol
                    name
                    decimals
                }
                reserve0
                reserve1
                reserveUSD
                volumeUSD
                token0Price
                token1Price
                txCount
                createdAtTimestamp
            }
        }
        """
        
        try:
            data = await self._execute_query(query, {'limit': limit})
            pairs = data.get('pairs', [])
            
            logger.info(
                "quickswap_pairs_fetched",
                count=len(pairs),
                total_volume_usd=sum(float(p.get('volumeUSD', 0)) for p in pairs)
            )
            
            return pairs
            
        except Exception as e:
            logger.error("quickswap_get_top_pairs_failed", error=str(e))
            return []
    
    async def get_pair_data(self, pair_address: str) -> Optional[Dict[str, Any]]:
        """Get detailed data for a specific pair"""
        query = """
        query GetPairData($pairAddress: String!) {
            pair(id: $pairAddress) {
                id
                token0 {
                    id
                    symbol
                    name
                    decimals
                }
                token1 {
                    id
                    symbol
                    name
                    decimals
                }
                reserve0
                reserve1
                reserveUSD
                volumeUSD
                token0Price
                token1Price
                txCount
                createdAtTimestamp
            }
        }
        """
        
        try:
            data = await self._execute_query(query, {'pairAddress': pair_address.lower()})
            return data.get('pair')
            
        except Exception as e:
            logger.error(
                "quickswap_get_pair_data_failed",
                pair_address=pair_address,
                error=str(e)
            )
            return None
    
    async def get_pair_hourly_data(self, pair_address: str, hours: int = 24) -> List[Dict[str, Any]]:
        """Get hourly data for a pair"""
        timestamp_from = int((datetime.now() - timedelta(hours=hours)).timestamp())
        
        query = """
        query GetPairHourlyData($pairAddress: String!, $timestampFrom: Int!) {
            pairHourDatas(
                where: {
                    pair: $pairAddress,
                    hourStartUnix_gte: $timestampFrom
                },
                orderBy: hourStartUnix,
                orderDirection: desc,
                first: 1000
            ) {
                id
                hourStartUnix
                reserve0
                reserve1
                reserveUSD
                hourlyVolumeToken0
                hourlyVolumeToken1
                hourlyVolumeUSD
                hourlyTxns
            }
        }
        """
        
        try:
            data = await self._execute_query(query, {
                'pairAddress': pair_address.lower(),
                'timestampFrom': timestamp_from
            })
            
            return data.get('pairHourDatas', [])
            
        except Exception as e:
            logger.error(
                "quickswap_get_pair_hourly_data_failed",
                pair_address=pair_address,
                error=str(e)
            )
            return []

    async def get_historical_data(self, token_address: str, base_token: str, hours: int = 24) -> List[Dict[str, Any]]:
        """Get historical price data for volatility analysis"""
        try:
            # Find pairs containing this token
            top_pairs = await self.get_top_pairs(50)
            relevant_pairs = []

            for pair in top_pairs:
                token0_id = pair.get('token0', {}).get('id', '').lower()
                token1_id = pair.get('token1', {}).get('id', '').lower()

                if token_address.lower() in [token0_id, token1_id]:
                    relevant_pairs.append(pair)

            if not relevant_pairs:
                return []

            # Get hourly data for the most liquid pair
            best_pair = max(relevant_pairs, key=lambda p: float(p.get('reserveUSD', '0')))
            pair_address = best_pair.get('id')

            hourly_data = await self.get_pair_hourly_data(pair_address, hours)

            # Convert to price history format
            price_history = []
            for data_point in hourly_data:
                # Determine if our token is token0 or token1
                token0_id = best_pair.get('token0', {}).get('id', '').lower()
                is_token0 = token_address.lower() == token0_id

                price = float(data_point.get('token0Price' if is_token0 else 'token1Price', 0))
                volume = float(data_point.get('volumeUSD', 0))

                price_history.append({
                    'timestamp': int(data_point.get('periodStartUnix', 0)),
                    'price': price,
                    'volume': volume
                })

            return sorted(price_history, key=lambda x: x['timestamp'])

        except Exception as e:
            logger.error(f"QuickSwap historical data failed: {e}")
            return []


class UniswapV3Collector(BaseSubgraphCollector):
    """Uniswap V3 data collector using The Graph subgraph"""
    
    def __init__(self):
        super().__init__(config.UNISWAP_V3_SUBGRAPH_URL, "Uniswap V3")
    
    async def get_top_pools(self, limit: int = 20) -> List[Dict[str, Any]]:
        """Get top pools by TVL"""
        query = """
        query GetTopPools($limit: Int!) {
            pools(
                first: $limit,
                orderBy: totalValueLockedUSD,
                orderDirection: desc,
                where: {
                    totalValueLockedUSD_gt: "10000"
                }
            ) {
                id
                token0 {
                    id
                    symbol
                    name
                    decimals
                }
                token1 {
                    id
                    symbol
                    name
                    decimals
                }
                feeTier
                liquidity
                sqrtPrice
                token0Price
                token1Price
                volumeUSD
                totalValueLockedUSD
                txCount
                createdAtTimestamp
            }
        }
        """
        
        try:
            data = await self._execute_query(query, {'limit': limit})
            pools = data.get('pools', [])
            
            logger.info(
                "uniswap_v3_pools_fetched",
                count=len(pools),
                total_tvl_usd=sum(float(p.get('totalValueLockedUSD', 0)) for p in pools)
            )
            
            return pools
            
        except Exception as e:
            logger.error("uniswap_v3_get_top_pools_failed", error=str(e))
            return []
    
    async def get_pool_data(self, pool_address: str) -> Optional[Dict[str, Any]]:
        """Get detailed data for a specific pool"""
        query = """
        query GetPoolData($poolAddress: String!) {
            pool(id: $poolAddress) {
                id
                token0 {
                    id
                    symbol
                    name
                    decimals
                }
                token1 {
                    id
                    symbol
                    name
                    decimals
                }
                feeTier
                liquidity
                sqrtPrice
                token0Price
                token1Price
                volumeUSD
                totalValueLockedUSD
                txCount
                createdAtTimestamp
            }
        }
        """
        
        try:
            data = await self._execute_query(query, {'poolAddress': pool_address.lower()})
            return data.get('pool')
            
        except Exception as e:
            logger.error(
                "uniswap_v3_get_pool_data_failed",
                pool_address=pool_address,
                error=str(e)
            )
            return None
    
    async def get_pool_hourly_data(self, pool_address: str, hours: int = 24) -> List[Dict[str, Any]]:
        """Get hourly data for a pool"""
        timestamp_from = int((datetime.now() - timedelta(hours=hours)).timestamp())
        
        query = """
        query GetPoolHourlyData($poolAddress: String!, $timestampFrom: Int!) {
            poolHourDatas(
                where: {
                    pool: $poolAddress,
                    periodStartUnix_gte: $timestampFrom
                },
                orderBy: periodStartUnix,
                orderDirection: desc,
                first: 1000
            ) {
                id
                periodStartUnix
                liquidity
                sqrtPrice
                token0Price
                token1Price
                volumeToken0
                volumeToken1
                volumeUSD
                txCount
                open
                high
                low
                close
            }
        }
        """
        
        try:
            data = await self._execute_query(query, {
                'poolAddress': pool_address.lower(),
                'timestampFrom': timestamp_from
            })
            
            return data.get('poolHourDatas', [])
            
        except Exception as e:
            logger.error(
                "uniswap_v3_get_pool_hourly_data_failed",
                pool_address=pool_address,
                error=str(e)
            )
            return []

    async def get_historical_data(self, token_address: str, base_token: str, hours: int = 24) -> List[Dict[str, Any]]:
        """Get historical price data for volatility analysis"""
        try:
            # Find pools containing this token
            top_pools = await self.get_top_pools(50)
            relevant_pools = []

            for pool in top_pools:
                token0_id = pool.get('token0', {}).get('id', '').lower()
                token1_id = pool.get('token1', {}).get('id', '').lower()

                if token_address.lower() in [token0_id, token1_id]:
                    relevant_pools.append(pool)

            if not relevant_pools:
                return []

            # Get hourly data for the most liquid pool
            best_pool = max(relevant_pools, key=lambda p: float(p.get('totalValueLockedUSD', '0')))
            pool_address = best_pool.get('id')

            hourly_data = await self.get_pool_hourly_data(pool_address, hours)

            # Convert to price history format
            price_history = []
            for data_point in hourly_data:
                # Use close price as the price for this hour
                price = float(data_point.get('close', 0))
                volume = float(data_point.get('volumeUSD', 0))

                price_history.append({
                    'timestamp': int(data_point.get('periodStartUnix', 0)),
                    'price': price,
                    'volume': volume
                })

            return sorted(price_history, key=lambda x: x['timestamp'])

        except Exception as e:
            logger.error(f"Uniswap V3 historical data failed: {e}")
            return []


# Global collector instances
quickswap_collector = QuickSwapCollector()
uniswap_v3_collector = UniswapV3Collector()
