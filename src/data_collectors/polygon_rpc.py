"""
Polygon RPC Client for blockchain data collection
Handles Web3 connections, gas estimation, and transaction monitoring
"""

import asyncio
import time
from typing import Dict, List, Optional, Any, Union
from web3 import Web3
try:
    from web3.middleware import ExtraDataToPOAMiddleware
    geth_poa_middleware = ExtraDataToPOAMiddleware
except ImportError:
    # Fallback for older versions
    try:
        from web3.middleware import geth_poa_middleware
    except ImportError:
        geth_poa_middleware = None
import structlog
from config.settings import config

logger = structlog.get_logger(__name__)

# Import Alchemy client for fallback (lazy import to avoid circular dependencies)
_alchemy_client = None


def _get_alchemy_client():
    """Get Alchemy client instance (lazy loading)"""
    global _alchemy_client
    if _alchemy_client is None and config.ALCHEMY_API_KEY:
        try:
            from src.data_collectors.alchemy_client import AlchemyClient
            _alchemy_client = AlchemyClient()
        except Exception as e:
            logger.warning("alchemy_client_init_failed", error=str(e))
    return _alchemy_client


class PolygonRPCClient:
    """
    Polygon RPC client with connection pooling and error handling
    """
    
    def __init__(self, rpc_url: Optional[str] = None):
        """Initialize Polygon RPC client"""
        self.rpc_url = rpc_url or config.POLYGON_RPC_URL
        self.web3 = None
        self.connection_pool = []
        self.max_connections = 5
        self.retry_attempts = 3
        self.retry_delay = 1.0
        self.last_block_number = 0
        self.connection_health = True
        
        # Initialize connection
        self._initialize_connection()
        
    def _initialize_connection(self):
        """Initialize Web3 connection with middleware"""
        try:
            self.web3 = Web3(Web3.HTTPProvider(self.rpc_url))

            # Add PoA middleware for Polygon network if available
            if geth_poa_middleware:
                self.web3.middleware_onion.inject(geth_poa_middleware, layer=0)
                logger.info("poa_middleware_injected")
            else:
                logger.warning("poa_middleware_not_available")

            # Test connection
            if self.web3.is_connected():
                self.last_block_number = self.web3.eth.block_number
                logger.info(
                    "polygon_rpc_connected",
                    rpc_url=self.rpc_url,
                    latest_block=self.last_block_number
                )
                self.connection_health = True
            else:
                raise ConnectionError("Failed to connect to Polygon RPC")

        except Exception as e:
            logger.error("polygon_rpc_connection_failed", error=str(e))
            self.connection_health = False
            raise
    
    async def get_latest_block(self) -> Dict[str, Any]:
        """Get latest block information"""
        try:
            block = await self._execute_with_retry(
                lambda: self.web3.eth.get_block('latest', full_transactions=False),
                fallback_method='get_latest_block'
            )

            self.last_block_number = block['number']

            return {
                'number': block['number'],
                'hash': block['hash'].hex() if hasattr(block['hash'], 'hex') else block['hash'],
                'timestamp': block['timestamp'],
                'gas_used': block['gasUsed'],
                'gas_limit': block['gasLimit'],
                'transaction_count': len(block.get('transactions', []))
            }

        except Exception as e:
            logger.error("get_latest_block_failed", error=str(e))
            raise
    
    async def get_gas_price(self) -> Dict[str, int]:
        """Get current gas prices in Gwei"""
        try:
            gas_price_wei = await self._execute_with_retry(
                lambda: self.web3.eth.gas_price,
                fallback_method='get_gas_price'
            )

            gas_price_gwei = self.web3.from_wei(gas_price_wei, 'gwei')

            # Convert to int to avoid decimal issues
            gas_price_gwei = int(gas_price_gwei)

            # Estimate different priority levels
            return {
                'standard': gas_price_gwei,
                'fast': int(gas_price_gwei * 1.2),
                'instant': int(gas_price_gwei * 1.5)
            }

        except Exception as e:
            logger.error("get_gas_price_failed", error=str(e))
            return {
                'standard': config.GAS_PRICE_GWEI,
                'fast': int(config.GAS_PRICE_GWEI * 1.2),
                'instant': int(config.GAS_PRICE_GWEI * 1.5)
            }
    
    async def get_token_balance(self, token_address: str, wallet_address: str) -> float:
        """Get ERC-20 token balance for a wallet"""
        try:
            # ERC-20 balanceOf function signature
            balance_of_abi = [{
                "constant": True,
                "inputs": [{"name": "_owner", "type": "address"}],
                "name": "balanceOf",
                "outputs": [{"name": "balance", "type": "uint256"}],
                "type": "function"
            }]

            contract = self.web3.eth.contract(
                address=Web3.to_checksum_address(token_address),
                abi=balance_of_abi
            )

            balance = await self._execute_with_retry(
                lambda: contract.functions.balanceOf(
                    Web3.to_checksum_address(wallet_address)
                ).call(),
                fallback_method='get_token_balance'
            )

            # Get token decimals
            decimals = await self.get_token_decimals(token_address)

            return balance / (10 ** decimals)

        except Exception as e:
            # Try Alchemy fallback directly if Web3 method fails
            try:
                alchemy_client = _get_alchemy_client()
                if alchemy_client:
                    logger.info("trying_alchemy_fallback_for_token_balance")
                    return await alchemy_client.get_token_balance(token_address, wallet_address)
            except Exception as fallback_error:
                logger.error("alchemy_token_balance_fallback_failed", error=str(fallback_error))

            logger.error(
                "get_token_balance_failed",
                token_address=token_address,
                wallet_address=wallet_address,
                error=str(e)
            )
            return 0.0
    
    async def get_token_decimals(self, token_address: str) -> int:
        """Get token decimals"""
        try:
            decimals_abi = [{
                "constant": True,
                "inputs": [],
                "name": "decimals",
                "outputs": [{"name": "", "type": "uint8"}],
                "type": "function"
            }]

            contract = self.web3.eth.contract(
                address=Web3.to_checksum_address(token_address),
                abi=decimals_abi
            )

            decimals = await self._execute_with_retry(
                lambda: contract.functions.decimals().call(),
                fallback_method='get_token_decimals'
            )

            return decimals

        except Exception as e:
            # Try Alchemy fallback directly if Web3 method fails
            try:
                alchemy_client = _get_alchemy_client()
                if alchemy_client:
                    logger.info("trying_alchemy_fallback_for_token_decimals")
                    return await alchemy_client.get_token_decimals(token_address)
            except Exception as fallback_error:
                logger.error("alchemy_token_decimals_fallback_failed", error=str(fallback_error))

            logger.error(
                "get_token_decimals_failed",
                token_address=token_address,
                error=str(e)
            )
            return 18  # Default to 18 decimals
    
    async def get_transaction_receipt(self, tx_hash: str) -> Optional[Dict[str, Any]]:
        """Get transaction receipt"""
        try:
            receipt = await self._execute_with_retry(
                lambda: self.web3.eth.get_transaction_receipt(tx_hash),
                fallback_method='get_transaction_receipt'
            )

            return {
                'transaction_hash': receipt['transactionHash'].hex() if hasattr(receipt['transactionHash'], 'hex') else receipt['transactionHash'],
                'block_number': receipt['blockNumber'],
                'gas_used': receipt['gasUsed'],
                'status': receipt['status'],
                'logs': [log for log in receipt['logs']]
            }

        except Exception as e:
            # Try Alchemy fallback directly if Web3 method fails
            try:
                alchemy_client = _get_alchemy_client()
                if alchemy_client:
                    logger.info("trying_alchemy_fallback_for_transaction_receipt")
                    return await alchemy_client.get_transaction_receipt(tx_hash)
            except Exception as fallback_error:
                logger.error("alchemy_transaction_receipt_fallback_failed", error=str(fallback_error))

            logger.error(
                "get_transaction_receipt_failed",
                tx_hash=tx_hash,
                error=str(e)
            )
            return None
    
    async def estimate_gas(self, transaction: Dict[str, Any]) -> int:
        """Estimate gas for a transaction"""
        try:
            gas_estimate = await self._execute_with_retry(
                lambda: self.web3.eth.estimate_gas(transaction),
                fallback_method='estimate_gas'
            )

            # Add 20% buffer for safety
            return int(gas_estimate * 1.2)

        except Exception as e:
            # Try Alchemy fallback directly if Web3 method fails
            try:
                alchemy_client = _get_alchemy_client()
                if alchemy_client:
                    logger.info("trying_alchemy_fallback_for_estimate_gas")
                    return await alchemy_client.estimate_gas(transaction)
            except Exception as fallback_error:
                logger.error("alchemy_estimate_gas_fallback_failed", error=str(fallback_error))

            logger.error(
                "estimate_gas_failed",
                transaction=transaction,
                error=str(e)
            )
            return 21000  # Default gas limit
    
    async def _execute_with_retry(self, func, max_retries: Optional[int] = None, fallback_method: Optional[str] = None):
        """Execute function with retry logic and Alchemy fallback"""
        max_retries = max_retries or self.retry_attempts

        for attempt in range(max_retries):
            try:
                if asyncio.iscoroutinefunction(func):
                    return await func()
                else:
                    return func()

            except Exception as e:
                if attempt == max_retries - 1:
                    # Try Alchemy fallback if available and method is supported
                    if fallback_method:
                        try:
                            return await self._try_alchemy_fallback(fallback_method)
                        except Exception as fallback_error:
                            logger.error(
                                "alchemy_fallback_failed",
                                method=fallback_method,
                                error=str(fallback_error)
                            )
                    raise e

                logger.warning(
                    "rpc_retry_attempt",
                    attempt=attempt + 1,
                    max_retries=max_retries,
                    error=str(e)
                )

                await asyncio.sleep(self.retry_delay * (2 ** attempt))

                # Try to reconnect if connection is lost
                if not self.web3.is_connected():
                    self._initialize_connection()

    async def _try_alchemy_fallback(self, method: str, *args, **kwargs):
        """Try to execute method using Alchemy as fallback"""
        alchemy_client = _get_alchemy_client()
        if not alchemy_client:
            raise Exception("Alchemy client not available")

        logger.info("using_alchemy_fallback", method=method)

        # Map method names to Alchemy client methods
        method_map = {
            'get_latest_block': alchemy_client.get_latest_block,
            'get_gas_price': alchemy_client.get_gas_price,
            'get_token_balance': alchemy_client.get_token_balance,
            'get_token_decimals': alchemy_client.get_token_decimals,
            'get_transaction_receipt': alchemy_client.get_transaction_receipt,
            'estimate_gas': alchemy_client.estimate_gas,
        }

        if method not in method_map:
            raise Exception(f"Method {method} not supported by Alchemy fallback")

        return await method_map[method](*args, **kwargs)
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on RPC connection and Alchemy fallback"""
        primary_health = {'healthy': False, 'error': 'Unknown'}
        alchemy_health = {'healthy': False, 'error': 'Not configured'}

        # Check primary RPC
        try:
            start_time = time.time()
            latest_block = await self.get_latest_block()
            response_time = time.time() - start_time

            self.connection_health = True
            primary_health = {
                'healthy': True,
                'latest_block': latest_block['number'],
                'response_time_ms': int(response_time * 1000),
                'rpc_url': self.rpc_url
            }

        except Exception as e:
            self.connection_health = False
            logger.error("rpc_health_check_failed", error=str(e))
            primary_health = {
                'healthy': False,
                'error': str(e),
                'rpc_url': self.rpc_url
            }

        # Check Alchemy fallback
        try:
            alchemy_client = _get_alchemy_client()
            if alchemy_client:
                alchemy_health = await alchemy_client.health_check()
        except Exception as e:
            logger.warning("alchemy_health_check_failed", error=str(e))
            alchemy_health = {'healthy': False, 'error': str(e)}

        return {
            'primary_rpc': primary_health,
            'alchemy_fallback': alchemy_health,
            'overall_healthy': primary_health['healthy'] or alchemy_health['healthy']
        }
    
    def is_connected(self) -> bool:
        """Check if RPC connection is healthy"""
        return self.connection_health and self.web3 and self.web3.is_connected()
    
    async def close(self):
        """Close RPC connections"""
        logger.info("polygon_rpc_closing_connections")
        # Web3.py doesn't require explicit connection closing
        self.connection_health = False

        # Close Alchemy client if available
        try:
            alchemy_client = _get_alchemy_client()
            if alchemy_client:
                await alchemy_client.close()
        except Exception as e:
            logger.warning("alchemy_client_close_failed", error=str(e))


# Global RPC client instance
polygon_rpc = PolygonRPCClient()
