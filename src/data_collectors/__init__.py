"""
Data collectors package for the Polygon MATIC Signal Bot
Handles all external data sources and API integrations
"""

from .polygon_rpc import PolygonRPCClient
from .dex_collectors import QuickSwapCollector, UniswapV3Collector
from .alchemy_market_client import AlchemyMarketClient
from .simple_asset_provider import SimpleAssetProvider
from .price_aggregator import PriceAggregator
from .asset_selector import AssetSelector

__all__ = [
    'PolygonRPCClient',
    'QuickSwapCollector',
    'UniswapV3Collector',
    'AlchemyMarketClient',
    'SimpleAssetProvider',
    'PriceAggregator',
    'AssetSelector'
]