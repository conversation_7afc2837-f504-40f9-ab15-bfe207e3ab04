"""
Data Caching System for the Signal Bot
Handles Redis and SQLite caching with TTL management
"""

import asyncio
import json
import sqlite3
import time
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
import aioredis
import structlog
from config.settings import config

logger = structlog.get_logger(__name__)


class CacheManager:
    """
    Unified cache manager supporting Redis and SQLite backends
    """
    
    def __init__(self):
        self.redis_client = None
        self.sqlite_conn = None
        self.sqlite_path = config.SQLITE_DB_PATH
        self.redis_url = config.REDIS_URL
        self.default_ttl = 300  # 5 minutes default TTL
        self.use_redis = True
        
        # Initialize storage (will be done lazily)
        self._initialized = False
    
    async def _ensure_initialized(self):
        """Ensure cache backends are initialized"""
        if not self._initialized:
            await self._initialize()
            self._initialized = True

    async def _initialize(self):
        """Initialize cache backends"""
        try:
            # Try Redis first
            await self._init_redis()
        except Exception as e:
            logger.warning("redis_init_failed", error=str(e))
            self.use_redis = False

        # Always initialize SQLite as fallback
        await self._init_sqlite()
    
    async def _init_redis(self):
        """Initialize Redis connection"""
        try:
            self.redis_client = await aioredis.from_url(
                self.redis_url,
                encoding="utf-8",
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5
            )
            
            # Test connection
            await self.redis_client.ping()
            logger.info("redis_cache_initialized", url=self.redis_url)
            
        except Exception as e:
            logger.error("redis_init_failed", error=str(e))
            self.redis_client = None
            raise
    
    async def _init_sqlite(self):
        """Initialize SQLite database"""
        try:
            # Create directory if it doesn't exist
            import os
            os.makedirs(os.path.dirname(self.sqlite_path), exist_ok=True)
            
            self.sqlite_conn = sqlite3.connect(self.sqlite_path, check_same_thread=False)
            
            # Create cache table
            self.sqlite_conn.execute("""
                CREATE TABLE IF NOT EXISTS cache (
                    key TEXT PRIMARY KEY,
                    value TEXT NOT NULL,
                    expires_at REAL NOT NULL,
                    created_at REAL NOT NULL
                )
            """)
            
            # Create index for expiration cleanup
            self.sqlite_conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_expires_at ON cache(expires_at)
            """)
            
            self.sqlite_conn.commit()
            logger.info("sqlite_cache_initialized", path=self.sqlite_path)
            
        except Exception as e:
            logger.error("sqlite_init_failed", error=str(e))
            raise
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Set cache value with TTL"""
        await self._ensure_initialized()
        ttl = ttl or self.default_ttl
        expires_at = time.time() + ttl
        
        try:
            # Serialize value
            serialized_value = json.dumps(value, default=str)
            
            # Try Redis first
            if self.use_redis and self.redis_client:
                try:
                    await self.redis_client.setex(key, ttl, serialized_value)
                    logger.debug("cache_set_redis", key=key, ttl=ttl)
                    return True
                except Exception as e:
                    logger.warning("redis_set_failed", key=key, error=str(e))
            
            # Fallback to SQLite
            if self.sqlite_conn:
                cursor = self.sqlite_conn.cursor()
                cursor.execute("""
                    INSERT OR REPLACE INTO cache (key, value, expires_at, created_at)
                    VALUES (?, ?, ?, ?)
                """, (key, serialized_value, expires_at, time.time()))
                self.sqlite_conn.commit()
                logger.debug("cache_set_sqlite", key=key, ttl=ttl)
                return True
            
            return False
            
        except Exception as e:
            logger.error("cache_set_failed", key=key, error=str(e))
            return False
    
    async def get(self, key: str) -> Optional[Any]:
        """Get cache value"""
        await self._ensure_initialized()
        try:
            # Try Redis first
            if self.use_redis and self.redis_client:
                try:
                    value = await self.redis_client.get(key)
                    if value is not None:
                        logger.debug("cache_hit_redis", key=key)
                        return json.loads(value)
                except Exception as e:
                    logger.warning("redis_get_failed", key=key, error=str(e))
            
            # Fallback to SQLite
            if self.sqlite_conn:
                cursor = self.sqlite_conn.cursor()
                cursor.execute("""
                    SELECT value, expires_at FROM cache 
                    WHERE key = ? AND expires_at > ?
                """, (key, time.time()))
                
                row = cursor.fetchone()
                if row:
                    logger.debug("cache_hit_sqlite", key=key)
                    return json.loads(row[0])
            
            logger.debug("cache_miss", key=key)
            return None
            
        except Exception as e:
            logger.error("cache_get_failed", key=key, error=str(e))
            return None
    
    async def delete(self, key: str) -> bool:
        """Delete cache key"""
        try:
            success = False
            
            # Delete from Redis
            if self.use_redis and self.redis_client:
                try:
                    await self.redis_client.delete(key)
                    success = True
                except Exception as e:
                    logger.warning("redis_delete_failed", key=key, error=str(e))
            
            # Delete from SQLite
            if self.sqlite_conn:
                cursor = self.sqlite_conn.cursor()
                cursor.execute("DELETE FROM cache WHERE key = ?", (key,))
                self.sqlite_conn.commit()
                success = True
            
            logger.debug("cache_delete", key=key, success=success)
            return success
            
        except Exception as e:
            logger.error("cache_delete_failed", key=key, error=str(e))
            return False
    
    async def exists(self, key: str) -> bool:
        """Check if key exists in cache"""
        value = await self.get(key)
        return value is not None
    
    async def clear_expired(self) -> int:
        """Clear expired entries from SQLite cache"""
        try:
            if not self.sqlite_conn:
                return 0
            
            cursor = self.sqlite_conn.cursor()
            cursor.execute("DELETE FROM cache WHERE expires_at <= ?", (time.time(),))
            deleted_count = cursor.rowcount
            self.sqlite_conn.commit()
            
            logger.info("cache_expired_cleared", count=deleted_count)
            return deleted_count
            
        except Exception as e:
            logger.error("cache_clear_expired_failed", error=str(e))
            return 0
    
    async def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        stats = {
            'redis_available': self.use_redis and self.redis_client is not None,
            'sqlite_available': self.sqlite_conn is not None,
            'sqlite_entries': 0,
            'sqlite_expired': 0
        }
        
        try:
            # SQLite stats
            if self.sqlite_conn:
                cursor = self.sqlite_conn.cursor()
                
                # Total entries
                cursor.execute("SELECT COUNT(*) FROM cache")
                stats['sqlite_entries'] = cursor.fetchone()[0]
                
                # Expired entries
                cursor.execute("SELECT COUNT(*) FROM cache WHERE expires_at <= ?", (time.time(),))
                stats['sqlite_expired'] = cursor.fetchone()[0]
            
            # Redis stats
            if self.use_redis and self.redis_client:
                try:
                    info = await self.redis_client.info()
                    stats['redis_memory_used'] = info.get('used_memory_human', 'Unknown')
                    stats['redis_connected_clients'] = info.get('connected_clients', 0)
                except Exception as e:
                    logger.warning("redis_stats_failed", error=str(e))
            
            return stats
            
        except Exception as e:
            logger.error("cache_stats_failed", error=str(e))
            return stats
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on cache systems"""
        health = {
            'redis': {'healthy': False, 'error': None},
            'sqlite': {'healthy': False, 'error': None}
        }
        
        # Test Redis
        if self.use_redis and self.redis_client:
            try:
                await self.redis_client.ping()
                health['redis']['healthy'] = True
            except Exception as e:
                health['redis']['error'] = str(e)
        
        # Test SQLite
        if self.sqlite_conn:
            try:
                cursor = self.sqlite_conn.cursor()
                cursor.execute("SELECT 1")
                cursor.fetchone()
                health['sqlite']['healthy'] = True
            except Exception as e:
                health['sqlite']['error'] = str(e)
        
        return health
    
    async def close(self):
        """Close cache connections"""
        try:
            if self.redis_client:
                await self.redis_client.close()
                logger.info("redis_cache_closed")
            
            if self.sqlite_conn:
                self.sqlite_conn.close()
                logger.info("sqlite_cache_closed")
                
        except Exception as e:
            logger.error("cache_close_failed", error=str(e))


# Cache key generators
class CacheKeys:
    """Cache key generators for different data types"""
    
    @staticmethod
    def price_data(symbol: str, timeframe: str) -> str:
        return f"price:{symbol}:{timeframe}"
    
    @staticmethod
    def dex_pair_data(dex: str, pair_address: str) -> str:
        return f"dex:{dex}:pair:{pair_address}"
    
    @staticmethod
    def liquidation_data(symbol: str, timeframe: str) -> str:
        return f"liquidation:{symbol}:{timeframe}"
    
    @staticmethod
    def sentiment_data(symbol: str) -> str:
        return f"sentiment:{symbol}"
    
    @staticmethod
    def top_assets() -> str:
        return "assets:top"
    
    @staticmethod
    def gas_price() -> str:
        return "gas:price"


# Global cache manager instance
cache_manager = CacheManager()
