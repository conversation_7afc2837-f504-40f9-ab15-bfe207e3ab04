"""
Real-time Price Feed Aggregator
Aggregates price data from multiple sources with conflict resolution
"""

import asyncio
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
import statistics
import structlog
from .simple_asset_provider import simple_asset_provider
from .polygon_rpc import polygon_rpc
from .cache_manager import cache_manager, CacheKeys
from config.settings import config

logger = structlog.get_logger(__name__)


class PriceAggregator:
    """
    Aggregates price data from multiple DEX sources
    """
    
    def __init__(self):
        self.sources = {
            'simple_provider': simple_asset_provider
        }
        self.price_cache_ttl = 30  # 30 seconds for price data
        self.pair_cache_ttl = 300  # 5 minutes for pair metadata
        self.min_sources_for_consensus = 2
        self.max_price_deviation = 0.05  # 5% max deviation between sources
        
    async def get_token_price(self, token_address: str, base_token: str = "USDC") -> Optional[Dict[str, Any]]:
        """Get aggregated token price from multiple sources"""
        cache_key = CacheKeys.price_data(f"{token_address}_{base_token}", "current")
        
        # Check cache first
        cached_price = await cache_manager.get(cache_key)
        if cached_price:
            logger.debug("price_cache_hit", token_address=token_address, base_token=base_token)
            return cached_price
        
        try:
            # Collect prices from all sources
            price_sources = await self._collect_price_sources(token_address, base_token)
            
            if not price_sources:
                logger.warning("no_price_sources", token_address=token_address, base_token=base_token)
                return None
            
            # Aggregate prices
            aggregated_price = self._aggregate_prices(price_sources)
            
            # Cache result
            await cache_manager.set(cache_key, aggregated_price, self.price_cache_ttl)
            
            logger.info(
                "price_aggregated",
                token_address=token_address,
                base_token=base_token,
                price=aggregated_price['price'],
                sources=len(price_sources)
            )
            
            return aggregated_price
            
        except Exception as e:
            logger.error(
                "price_aggregation_failed",
                token_address=token_address,
                base_token=base_token,
                error=str(e)
            )
            return None
    
    async def _collect_price_sources(self, token_address: str, base_token: str) -> List[Dict[str, Any]]:
        """Collect prices from all available sources"""
        price_sources = []
        
        # Get relevant pairs from Simple Asset Provider
        tasks = [
            self._get_simple_provider_price(token_address, base_token),
            self._get_simple_provider_pools_price(token_address, base_token)
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        for i, result in enumerate(results):
            if not isinstance(result, Exception) and result:
                price_sources.append(result)
        
        return price_sources
    
    async def _get_simple_provider_price(self, token_address: str, base_token: str) -> Optional[Dict[str, Any]]:
        """Get price from Simple Asset Provider"""
        try:
            # Find relevant pairs
            top_pairs = await simple_asset_provider.get_top_pairs(50)
            
            for pair in top_pairs:
                token0_addr = pair['token0']['id'].lower()
                token1_addr = pair['token1']['id'].lower()
                token0_symbol = pair['token0']['symbol']
                token1_symbol = pair['token1']['symbol']
                
                target_token_addr = token_address.lower()
                
                # Check if this pair contains our target token and base token
                if ((token0_addr == target_token_addr and token1_symbol == base_token) or
                    (token1_addr == target_token_addr and token0_symbol == base_token)):
                    
                    # Calculate price based on reserves
                    if token0_addr == target_token_addr:
                        # Token is token0, price in terms of token1
                        price = float(pair['token0Price'])
                    else:
                        # Token is token1, price in terms of token0
                        price = float(pair['token1Price'])
                    
                    return {
                        'source': 'simple_provider',
                        'price': price,
                        'pair_address': pair['id'],
                        'liquidity_usd': float(pair['reserveUSD']),
                        'volume_24h_usd': float(pair['volumeUSD']),
                        'timestamp': datetime.now().isoformat()
                    }
            
            return None
            
        except Exception as e:
            logger.error("simple_provider_price_failed", token_address=token_address, error=str(e))
            return None
    
    async def _get_simple_provider_pools_price(self, token_address: str, base_token: str) -> Optional[Dict[str, Any]]:
        """Get price from Simple Asset Provider pools"""
        try:
            # Find relevant pools
            top_pools = await simple_asset_provider.get_top_pools(50)

            for pool in top_pools:
                token0_addr = pool['token0']['id'].lower()
                token1_addr = pool['token1']['id'].lower()
                token0_symbol = pool['token0']['symbol']
                token1_symbol = pool['token1']['symbol']

                target_token_addr = token_address.lower()

                # Check if this pool contains our target token and base token
                if ((token0_addr == target_token_addr and token1_symbol == base_token) or
                    (token1_addr == target_token_addr and token0_symbol == base_token)):

                    # Calculate price based on current price
                    if token0_addr == target_token_addr:
                        # Token is token0, price in terms of token1
                        price = float(pool['token0Price'])
                    else:
                        # Token is token1, price in terms of token0
                        price = float(pool['token1Price'])

                    return {
                        'source': 'simple_provider_pools',
                        'price': price,
                        'pool_address': pool['id'],
                        'liquidity_usd': float(pool['totalValueLockedUSD']),
                        'volume_24h_usd': float(pool['volumeUSD']),
                        'fee_tier': pool['feeTier'],
                        'timestamp': datetime.now().isoformat()
                    }

            return None

        except Exception as e:
            logger.error("simple_provider_pools_price_failed", token_address=token_address, error=str(e))
            return None
    
    def _aggregate_prices(self, price_sources: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Aggregate prices from multiple sources with conflict resolution"""
        if not price_sources:
            raise ValueError("No price sources available")
        
        if len(price_sources) == 1:
            # Single source, return as-is with metadata
            source = price_sources[0]
            return {
                'price': source['price'],
                'confidence': 0.7,  # Lower confidence for single source
                'sources': [source],
                'aggregation_method': 'single_source',
                'timestamp': datetime.now().isoformat()
            }
        
        # Multiple sources - perform aggregation
        prices = [source['price'] for source in price_sources]
        
        # Check for outliers
        median_price = statistics.median(prices)
        filtered_sources = []
        
        for source in price_sources:
            deviation = abs(source['price'] - median_price) / median_price
            if deviation <= self.max_price_deviation:
                filtered_sources.append(source)
            else:
                logger.warning(
                    "price_outlier_detected",
                    source=source['source'],
                    price=source['price'],
                    median=median_price,
                    deviation=deviation
                )
        
        if not filtered_sources:
            # All sources are outliers, use median
            filtered_sources = price_sources
        
        # Calculate weighted average based on liquidity
        total_weight = 0
        weighted_sum = 0
        
        for source in filtered_sources:
            # Use liquidity as weight (higher liquidity = higher weight)
            weight = source.get('liquidity_usd', 1000)  # Default weight if no liquidity data
            weighted_sum += source['price'] * weight
            total_weight += weight
        
        aggregated_price = weighted_sum / total_weight if total_weight > 0 else median_price
        
        # Calculate confidence based on source agreement
        price_std = statistics.stdev([s['price'] for s in filtered_sources]) if len(filtered_sources) > 1 else 0
        price_cv = price_std / aggregated_price if aggregated_price > 0 else 1
        confidence = max(0.5, 1.0 - price_cv)  # Higher agreement = higher confidence
        
        return {
            'price': aggregated_price,
            'confidence': confidence,
            'sources': filtered_sources,
            'aggregation_method': 'weighted_average',
            'price_std': price_std,
            'outliers_removed': len(price_sources) - len(filtered_sources),
            'timestamp': datetime.now().isoformat()
        }
    
    async def get_multiple_prices(self, token_addresses: List[str], base_token: str = "USDC") -> Dict[str, Any]:
        """Get prices for multiple tokens concurrently"""
        tasks = [
            self.get_token_price(token_addr, base_token)
            for token_addr in token_addresses
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        prices = {}
        for i, result in enumerate(results):
            token_addr = token_addresses[i]
            if not isinstance(result, Exception) and result:
                prices[token_addr] = result
            else:
                logger.error(
                    "multi_price_failed",
                    token_address=token_addr,
                    error=str(result) if isinstance(result, Exception) else "No data"
                )
        
        return prices
    
    async def get_price_history(self, token_address: str, base_token: str = "USDC", hours: int = 24) -> List[Dict[str, Any]]:
        """Get historical price data"""
        try:
            # Get historical data from both sources
            tasks = [
                self._get_quickswap_history(token_address, base_token, hours),
                self._get_uniswap_v3_history(token_address, base_token, hours)
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Combine and sort historical data
            all_history = []
            for result in results:
                if not isinstance(result, Exception) and result:
                    all_history.extend(result)
            
            # Sort by timestamp
            all_history.sort(key=lambda x: x['timestamp'])
            
            return all_history
            
        except Exception as e:
            logger.error(
                "price_history_failed",
                token_address=token_address,
                base_token=base_token,
                error=str(e)
            )
            return []
    
    async def _get_quickswap_history(self, token_address: str, base_token: str, hours: int) -> List[Dict[str, Any]]:
        """Get QuickSwap historical data from subgraph"""
        try:
            from .dex_collectors import quickswap_collector

            # Get historical data from QuickSwap
            historical_data = await quickswap_collector.get_historical_data(token_address, base_token, hours)

            if not historical_data:
                return []

            # Format the data for consistency
            formatted_data = []
            for data_point in historical_data:
                formatted_data.append({
                    'timestamp': data_point.get('timestamp'),
                    'price': float(data_point.get('price', 0)),
                    'volume': float(data_point.get('volume', 0)),
                    'source': 'quickswap'
                })

            return formatted_data

        except Exception as e:
            logger.error(f"QuickSwap historical data failed: {e}")
            return []
    
    async def _get_uniswap_v3_history(self, token_address: str, base_token: str, hours: int) -> List[Dict[str, Any]]:
        """Get Uniswap V3 historical data from subgraph"""
        try:
            from .dex_collectors import uniswap_v3_collector

            # Get historical data from Uniswap V3
            historical_data = await uniswap_v3_collector.get_historical_data(token_address, base_token, hours)

            if not historical_data:
                return []

            # Format the data for consistency
            formatted_data = []
            for data_point in historical_data:
                formatted_data.append({
                    'timestamp': data_point.get('timestamp'),
                    'price': float(data_point.get('price', 0)),
                    'volume': float(data_point.get('volume', 0)),
                    'source': 'uniswap_v3'
                })

            return formatted_data

        except Exception as e:
            logger.error(f"Uniswap V3 historical data failed: {e}")
            return []

    async def get_historical_prices(self, token_address: str, base_token: str = "USDC", hours: int = 24, interval: str = "1h") -> List[Dict[str, Any]]:
        """Get historical price data from real sources"""
        try:
            historical_prices = []

            # Try to get historical data from QuickSwap
            quickswap_history = await self._get_quickswap_history(token_address, base_token, hours)
            if quickswap_history:
                historical_prices.extend(quickswap_history)

            # Try to get historical data from Uniswap V3
            uniswap_history = await self._get_uniswap_v3_history(token_address, base_token, hours)
            if uniswap_history:
                historical_prices.extend(uniswap_history)

            # If no historical data available, return empty list
            if not historical_prices:
                logger.warning(
                    "no_historical_data_available",
                    token_address=token_address,
                    base_token=base_token,
                    hours=hours
                )
                return []

            # Sort by timestamp
            historical_prices.sort(key=lambda x: x['timestamp'])

            logger.info(
                "historical_prices_fetched",
                token_address=token_address,
                base_token=base_token,
                hours=hours,
                data_points=len(historical_prices)
            )

            return historical_prices

        except Exception as e:
            logger.error(
                "get_historical_prices_failed",
                token_address=token_address,
                base_token=base_token,
                error=str(e)
            )
            return []


# Global price aggregator instance
price_aggregator = PriceAggregator()
