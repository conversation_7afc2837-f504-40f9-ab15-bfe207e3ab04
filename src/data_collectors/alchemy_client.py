"""
Alchemy API Client for Polygon blockchain data
Provides fallback RPC functionality when primary Polygon RPC fails
"""

import asyncio
import aiohttp
import time
from typing import Dict, List, Optional, Any, Union
import structlog
from config.settings import config

logger = structlog.get_logger(__name__)


class AlchemyClient:
    """
    Alchemy API client for Polygon blockchain data
    Provides Web3-compatible interface using Alchemy's REST API
    """
    
    def __init__(self, api_key: Optional[str] = None, rpc_url: Optional[str] = None):
        """Initialize Alchemy client"""
        self.api_key = api_key or config.ALCHEMY_API_KEY
        self.rpc_url = rpc_url or config.ALCHEMY_RPC_URL
        
        if not self.api_key:
            raise ValueError("Alchemy API key is required")
            
        # Ensure RPC URL includes the API key
        if not self.rpc_url.endswith(self.api_key):
            self.rpc_url = f"{self.rpc_url.rstrip('/')}/{self.api_key}"
            
        self.session = None
        self.retry_attempts = 3
        self.retry_delay = 1.0
        self.connection_health = True
        
    async def _get_session(self) -> aiohttp.ClientSession:
        """Get or create aiohttp session"""
        if self.session is None or self.session.closed:
            timeout = aiohttp.ClientTimeout(total=30)
            self.session = aiohttp.ClientSession(timeout=timeout)
        return self.session
    
    async def _make_rpc_call(self, method: str, params: List[Any] = None) -> Dict[str, Any]:
        """Make JSON-RPC call to Alchemy"""
        if params is None:
            params = []
            
        payload = {
            "jsonrpc": "2.0",
            "method": method,
            "params": params,
            "id": 1
        }
        
        session = await self._get_session()
        
        try:
            async with session.post(self.rpc_url, json=payload) as response:
                if response.status == 200:
                    data = await response.json()
                    if "error" in data:
                        raise Exception(f"RPC Error: {data['error']}")
                    return data.get("result")
                else:
                    raise Exception(f"HTTP Error: {response.status}")
                    
        except Exception as e:
            logger.error("alchemy_rpc_call_failed", method=method, error=str(e))
            raise
    
    async def get_latest_block(self) -> Dict[str, Any]:
        """Get latest block information"""
        try:
            block = await self._execute_with_retry(
                lambda: self._make_rpc_call("eth_getBlockByNumber", ["latest", False])
            )
            
            return {
                'number': int(block['number'], 16),
                'hash': block['hash'],
                'timestamp': int(block['timestamp'], 16),
                'gas_used': int(block['gasUsed'], 16),
                'gas_limit': int(block['gasLimit'], 16),
                'transaction_count': len(block.get('transactions', []))
            }
            
        except Exception as e:
            logger.error("alchemy_get_latest_block_failed", error=str(e))
            raise
    
    async def get_gas_price(self) -> Dict[str, int]:
        """Get current gas prices in Gwei"""
        try:
            gas_price_hex = await self._execute_with_retry(
                lambda: self._make_rpc_call("eth_gasPrice")
            )
            
            gas_price_wei = int(gas_price_hex, 16)
            gas_price_gwei = gas_price_wei // 10**9
            
            # Estimate different priority levels
            return {
                'standard': int(gas_price_gwei),
                'fast': int(gas_price_gwei * 1.2),
                'instant': int(gas_price_gwei * 1.5)
            }
            
        except Exception as e:
            logger.error("alchemy_get_gas_price_failed", error=str(e))
            return {
                'standard': config.GAS_PRICE_GWEI,
                'fast': int(config.GAS_PRICE_GWEI * 1.2),
                'instant': int(config.GAS_PRICE_GWEI * 1.5)
            }
    
    async def get_token_balance(self, token_address: str, wallet_address: str) -> float:
        """Get ERC-20 token balance for a wallet"""
        try:
            # ERC-20 balanceOf function call data
            function_signature = "0x70a08231"  # balanceOf(address)
            padded_address = wallet_address[2:].zfill(64)  # Remove 0x and pad to 64 chars
            call_data = function_signature + padded_address
            
            balance_hex = await self._execute_with_retry(
                lambda: self._make_rpc_call("eth_call", [{
                    "to": token_address,
                    "data": call_data
                }, "latest"])
            )
            
            balance = int(balance_hex, 16)
            
            # Get token decimals
            decimals = await self.get_token_decimals(token_address)
            
            return balance / (10 ** decimals)
            
        except Exception as e:
            logger.error(
                "alchemy_get_token_balance_failed",
                token_address=token_address,
                wallet_address=wallet_address,
                error=str(e)
            )
            return 0.0
    
    async def get_token_decimals(self, token_address: str) -> int:
        """Get token decimals"""
        try:
            # ERC-20 decimals function call data
            function_signature = "0x313ce567"  # decimals()
            
            decimals_hex = await self._execute_with_retry(
                lambda: self._make_rpc_call("eth_call", [{
                    "to": token_address,
                    "data": function_signature
                }, "latest"])
            )
            
            return int(decimals_hex, 16)
            
        except Exception as e:
            logger.error(
                "alchemy_get_token_decimals_failed",
                token_address=token_address,
                error=str(e)
            )
            return 18  # Default to 18 decimals
    
    async def get_transaction_receipt(self, tx_hash: str) -> Optional[Dict[str, Any]]:
        """Get transaction receipt"""
        try:
            receipt = await self._execute_with_retry(
                lambda: self._make_rpc_call("eth_getTransactionReceipt", [tx_hash])
            )
            
            if not receipt:
                return None
                
            return {
                'transaction_hash': receipt['transactionHash'],
                'block_number': int(receipt['blockNumber'], 16),
                'gas_used': int(receipt['gasUsed'], 16),
                'status': int(receipt['status'], 16),
                'logs': receipt.get('logs', [])
            }
            
        except Exception as e:
            logger.error(
                "alchemy_get_transaction_receipt_failed",
                tx_hash=tx_hash,
                error=str(e)
            )
            return None
    
    async def estimate_gas(self, transaction: Dict[str, Any]) -> int:
        """Estimate gas for a transaction"""
        try:
            gas_estimate_hex = await self._execute_with_retry(
                lambda: self._make_rpc_call("eth_estimateGas", [transaction])
            )
            
            gas_estimate = int(gas_estimate_hex, 16)
            
            # Add 20% buffer for safety
            return int(gas_estimate * 1.2)
            
        except Exception as e:
            logger.error(
                "alchemy_estimate_gas_failed",
                transaction=transaction,
                error=str(e)
            )
            return 21000  # Default gas limit
    
    async def _execute_with_retry(self, func, max_retries: Optional[int] = None):
        """Execute function with retry logic"""
        max_retries = max_retries or self.retry_attempts

        for attempt in range(max_retries):
            try:
                result = func()
                if asyncio.iscoroutine(result):
                    return await result
                else:
                    return result

            except Exception as e:
                if attempt == max_retries - 1:
                    raise e

                logger.warning(
                    "alchemy_retry_attempt",
                    attempt=attempt + 1,
                    max_retries=max_retries,
                    error=str(e)
                )

                await asyncio.sleep(self.retry_delay * (2 ** attempt))
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on Alchemy connection"""
        try:
            start_time = time.time()
            latest_block = await self.get_latest_block()
            response_time = time.time() - start_time
            
            self.connection_health = True
            
            return {
                'healthy': True,
                'latest_block': latest_block['number'],
                'response_time_ms': int(response_time * 1000),
                'provider': 'alchemy',
                'rpc_url': self.rpc_url
            }
            
        except Exception as e:
            self.connection_health = False
            logger.error("alchemy_health_check_failed", error=str(e))
            
            return {
                'healthy': False,
                'error': str(e),
                'provider': 'alchemy',
                'rpc_url': self.rpc_url
            }
    
    def is_connected(self) -> bool:
        """Check if Alchemy connection is healthy"""
        return self.connection_health
    
    async def close(self):
        """Close HTTP session"""
        if self.session and not self.session.closed:
            await self.session.close()
        logger.info("alchemy_client_closed")


# Global Alchemy client instance (only if API key is available)
alchemy_client = None
if config.ALCHEMY_API_KEY:
    alchemy_client = AlchemyClient()
