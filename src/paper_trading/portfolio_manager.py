"""
Portfolio Management System for Paper Trading - Phase 4 Implementation
Manages virtual portfolio, position tracking, and realistic trade execution
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import json

from config.settings import Config
from src.data_collectors.polygon_rpc import polygon_rpc
from src.data_collectors.price_aggregator import price_aggregator
from src.paper_trading.risk_manager import RiskManager
from src.paper_trading.trading_engine import get_trading_engine
from src.market_intelligence.volatility_analyzer import volatility_analyzer
from src.market_intelligence.inefficiency_detector import inefficiency_detector


class TradeStatus(Enum):
    """Trade execution status"""
    PENDING = "pending"
    FILLED = "filled"
    PARTIAL = "partial"
    CANCELLED = "cancelled"
    FAILED = "failed"


class PositionType(Enum):
    """Position type"""
    LONG = "long"
    SHORT = "short"


@dataclass
class Position:
    """Represents an open trading position"""
    asset: str
    position_type: PositionType
    entry_price: float
    quantity: float
    entry_time: datetime
    stop_loss: Optional[float] = None
    target_price: Optional[float] = None
    max_hold_time: Optional[int] = None  # minutes
    current_price: float = 0.0
    unrealized_pnl: float = 0.0
    gas_cost: float = 0.0
    slippage_cost: float = 0.0

    def update_current_price(self, price: float):
        """Update current price and calculate unrealized PnL"""
        self.current_price = price
        if self.position_type == PositionType.LONG:
            self.unrealized_pnl = (price - self.entry_price) * self.quantity
        else:  # SHORT
            self.unrealized_pnl = (self.entry_price - price) * self.quantity

    def get_total_cost(self) -> float:
        """Get total cost including gas and slippage"""
        return self.gas_cost + self.slippage_cost

    def should_exit(self) -> Tuple[bool, str]:
        """Check if position should be exited"""
        # Check stop loss
        if self.stop_loss:
            if self.position_type == PositionType.LONG and self.current_price <= self.stop_loss:
                return True, "stop_loss"
            elif self.position_type == PositionType.SHORT and self.current_price >= self.stop_loss:
                return True, "stop_loss"

        # Check target price
        if self.target_price:
            if self.position_type == PositionType.LONG and self.current_price >= self.target_price:
                return True, "target_reached"
            elif self.position_type == PositionType.SHORT and self.current_price <= self.target_price:
                return True, "target_reached"

        # Check max hold time
        if self.max_hold_time:
            hold_duration = (datetime.now() - self.entry_time).total_seconds() / 60
            if hold_duration >= self.max_hold_time:
                return True, "max_hold_time"

        return False, ""


@dataclass
class TradeExecution:
    """Represents a completed trade execution"""
    trade_id: str
    asset: str
    signal_direction: str
    entry_price: float
    exit_price: float
    quantity: float
    entry_time: datetime
    exit_time: datetime
    realized_pnl: float
    gas_cost: float
    slippage_cost: float
    exit_reason: str
    hold_time_minutes: float
    success: bool = True
    error_message: Optional[str] = None


class PortfolioManager:
    """Advanced portfolio management with realistic paper trading simulation"""

    def __init__(self, config: Config):
        self.config = config
        self.logger = logging.getLogger(__name__)

        # Initialize risk manager and trading engine
        self.risk_manager = RiskManager(config)
        self.trading_engine = get_trading_engine(config)

        # Portfolio state
        self.initial_balance = config.INITIAL_BALANCE_USD
        self.current_balance = self.initial_balance
        self.positions: Dict[str, Position] = {}
        self.trade_history: List[TradeExecution] = []

        # Risk management parameters
        self.max_position_size = config.MAX_POSITION_SIZE_PCT / 100  # Convert to decimal
        self.max_total_exposure = config.MAX_TOTAL_EXPOSURE_PCT / 100
        self.min_trade_size_usd = config.MIN_TRADE_SIZE_USD

        # Trading costs simulation
        self.base_slippage_bps = 10  # 0.1% base slippage
        self.liquidity_impact_factor = 0.0001  # Impact per $1000 trade size
        self.gas_cost_usd = 0.50  # Estimated gas cost per trade on Polygon

        # Performance tracking
        self.total_trades = 0
        self.winning_trades = 0
        self.total_pnl = 0.0
        self.total_fees = 0.0

    async def execute_trade(self, signal: Dict[str, Any], market_conditions: Dict[str, Any] = None) -> Dict[str, Any]:
        """Execute paper trade based on AI signal with advanced risk management"""
        try:
            # Validate signal
            validation_result = self._validate_signal(signal)
            if not validation_result['valid']:
                return {
                    'status': 'rejected',
                    'reason': validation_result['reason'],
                    'trade_id': None
                }

            # Get portfolio state for risk assessment
            portfolio_state = self.get_portfolio_summary()

            # Perform comprehensive risk assessment
            if market_conditions is None:
                market_conditions = {'condition': 'normal', 'total_liquidity': 100000}

            risk_assessment = self.risk_manager.assess_trade_risk(
                signal, portfolio_state, market_conditions
            )

            # Check if risk is acceptable
            if risk_assessment.risk_level.value == 'extreme':
                return {
                    'status': 'rejected',
                    'reason': f'Extreme risk detected: {risk_assessment.reasoning}',
                    'trade_id': None,
                    'risk_assessment': risk_assessment
                }

            # Apply risk-adjusted position sizing
            base_position_size = await self._calculate_position_size(signal)
            risk_adjusted_size = base_position_size * risk_assessment.position_size_multiplier

            if risk_adjusted_size < self.min_trade_size_usd:
                return {
                    'status': 'rejected',
                    'reason': 'Risk-adjusted position size too small',
                    'trade_id': None,
                    'risk_assessment': risk_assessment
                }

            # Update signal with risk management recommendations
            enhanced_signal = signal.copy()
            if risk_assessment.recommended_stop_loss:
                enhanced_signal['stop_loss'] = risk_assessment.recommended_stop_loss
            if risk_assessment.recommended_take_profit:
                enhanced_signal['target_price'] = risk_assessment.recommended_take_profit
            if risk_assessment.max_hold_time:
                enhanced_signal['max_hold_time'] = risk_assessment.max_hold_time

            # Use trading engine for realistic execution simulation
            execution_result = await self.trading_engine.simulate_trade_execution(
                enhanced_signal['asset'],
                enhanced_signal['signal_direction'],
                risk_adjusted_size,
                enhanced_signal['entry_price']
            )

            if execution_result['success']:
                # Create and store position
                position = self._create_position_from_execution(enhanced_signal, execution_result)
                self.positions[position.asset] = position

                # Update portfolio balance
                total_cost = execution_result['total_cost']
                self.current_balance -= total_cost

                self.logger.info(f"Trade executed: {enhanced_signal['asset']} {enhanced_signal['signal_direction']} "
                               f"size=${risk_adjusted_size:.2f} price=${execution_result['execution_price']:.6f} "
                               f"risk_level={risk_assessment.risk_level.value}")

                return {
                    'status': 'filled',
                    'trade_id': self._generate_trade_id(),
                    'fill_price': execution_result['execution_price'],
                    'quantity': execution_result['executed_quantity'],
                    'executed_size': execution_result['executed_size_usd'],
                    'total_cost': total_cost,
                    'gas_cost': execution_result['gas_cost'],
                    'slippage_cost': execution_result['slippage_cost'],
                    'position': position,
                    'risk_assessment': risk_assessment,
                    'market_impact': execution_result.get('market_impact', {}),
                    'execution_delay_ms': execution_result.get('execution_delay_ms', 0)
                }
            else:
                return {
                    'status': 'failed',
                    'reason': execution_result['error'],
                    'trade_id': self._generate_trade_id(),
                    'risk_assessment': risk_assessment
                }

        except Exception as e:
            self.logger.error(f"Trade execution failed: {e}")
            return {
                'status': 'error',
                'reason': str(e),
                'trade_id': None
            }

    async def update_positions(self) -> Dict[str, Any]:
        """Update all open positions with current market prices"""
        if not self.positions:
            return {'updated_positions': 0, 'closed_positions': 0}

        updated_count = 0
        closed_positions = []

        for asset, position in list(self.positions.items()):
            try:
                # Get current market price
                current_price = await self._get_current_price(asset)
                if current_price is None:
                    continue

                # Update position
                position.update_current_price(current_price)
                updated_count += 1

                # Check if position should be closed
                should_exit, exit_reason = position.should_exit()
                if should_exit:
                    close_result = await self._close_position(asset, exit_reason)
                    if close_result['success']:
                        closed_positions.append({
                            'asset': asset,
                            'reason': exit_reason,
                            'pnl': close_result['realized_pnl']
                        })

            except Exception as e:
                self.logger.error(f"Failed to update position {asset}: {e}")

        return {
            'updated_positions': updated_count,
            'closed_positions': len(closed_positions),
            'closed_details': closed_positions
        }

    async def _simulate_trade_execution(self, signal: Dict[str, Any], position_size_usd: float) -> Dict[str, Any]:
        """Simulate realistic trade execution with slippage and gas costs"""
        try:
            asset = signal['asset']
            entry_price = signal['entry_price']

            # Get current market data for slippage calculation
            market_data = await price_aggregator.get_token_price(
                self._extract_token_address(asset), "USDC"
            )

            if not market_data:
                return {
                    'success': False,
                    'error': 'No market data available',
                    'trade_id': self._generate_trade_id()
                }

            # Calculate slippage based on liquidity and trade size
            slippage_info = self._calculate_slippage(market_data, position_size_usd)

            # Calculate gas costs
            gas_cost = await self._estimate_gas_cost()

            # Apply slippage to entry price
            if signal['signal_direction'] == 'BUY':
                fill_price = entry_price * (1 + slippage_info['slippage_pct'])
            else:  # SELL
                fill_price = entry_price * (1 - slippage_info['slippage_pct'])

            # Calculate quantity
            quantity = position_size_usd / fill_price

            # Calculate total costs
            slippage_cost = abs(fill_price - entry_price) * quantity
            total_cost = position_size_usd + gas_cost + slippage_cost

            return {
                'success': True,
                'trade_id': self._generate_trade_id(),
                'fill_price': fill_price,
                'quantity': quantity,
                'gas_cost': gas_cost,
                'slippage_cost': slippage_cost,
                'total_cost': total_cost,
                'slippage_pct': slippage_info['slippage_pct'],
                'liquidity_impact': slippage_info['liquidity_impact']
            }

        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'trade_id': self._generate_trade_id()
            }

    def _validate_signal(self, signal: Dict[str, Any]) -> Dict[str, Any]:
        """Validate trading signal"""
        required_fields = ['asset', 'signal_direction', 'entry_price', 'confidence']

        for field in required_fields:
            if field not in signal:
                return {'valid': False, 'reason': f'Missing required field: {field}'}

        if signal['signal_direction'] not in ['BUY', 'SELL']:
            return {'valid': False, 'reason': 'Invalid signal direction'}

        if signal['entry_price'] <= 0:
            return {'valid': False, 'reason': 'Invalid entry price'}

        if signal['confidence'] < self.config.CONFIDENCE_THRESHOLD:
            return {'valid': False, 'reason': 'Confidence below threshold'}

        return {'valid': True, 'reason': None}

    async def _check_risk_limits(self, signal: Dict[str, Any]) -> Dict[str, Any]:
        """Check if trade meets risk management criteria"""
        asset = signal['asset']

        # Check if we already have a position in this asset
        if asset in self.positions:
            return {'allowed': False, 'reason': 'Position already exists for this asset'}

        # Check total exposure
        total_exposure = sum(pos.quantity * pos.current_price for pos in self.positions.values())
        exposure_pct = total_exposure / self.initial_balance

        if exposure_pct >= self.max_total_exposure:
            return {'allowed': False, 'reason': 'Maximum total exposure reached'}

        # Check available balance
        position_size = await self._calculate_position_size(signal)
        if position_size > self.current_balance * 0.95:  # Leave 5% buffer
            return {'allowed': False, 'reason': 'Insufficient balance'}

        return {'allowed': True, 'reason': None}

    async def _calculate_position_size(self, signal: Dict[str, Any]) -> float:
        """Calculate position size based on volatility analysis and signal strength"""
        try:
            # Get token address for volatility analysis
            token_address = self._extract_token_address(signal['asset'])

            # Get volatility analysis
            volatility_analysis = await volatility_analyzer.analyze_asset_volatility(token_address, "USDC")

            # Extract expected daily move from volatility analysis
            expected_daily_move = volatility_analysis.get('expected_daily_move_pct', 0)
            volatility_regime = volatility_analysis.get('volatility_regime', 'unknown_regime')
            analysis_quality = volatility_analysis.get('analysis_quality', {})

            # Base position size as percentage of portfolio
            base_size_pct = self.max_position_size

            # Volatility-based position sizing using the formula: realized_volatility ÷ 16 = expected_daily_move
            if expected_daily_move > 0 and analysis_quality.get('reliable_for_trading', False):
                # Adjust position size inversely to expected volatility
                # Higher volatility = smaller position size for risk management
                volatility_adjustment = min(2.0, max(0.3, 1.0 / (expected_daily_move / 100 + 0.01)))

                self.logger.info(f"Volatility-based sizing: expected_daily_move={expected_daily_move:.2f}%, "
                               f"adjustment={volatility_adjustment:.2f}, regime={volatility_regime}")
            else:
                # Fallback to conservative sizing if volatility analysis unavailable
                volatility_adjustment = 0.7
                self.logger.warning(f"Using fallback volatility adjustment for {signal['asset']}")

            # Adjust based on signal confidence and strength
            confidence_multiplier = signal.get('confidence', 0.5)
            strength_multiplier = signal.get('signal_strength', 0.5)

            # Combined signal multiplier (0.5 to 1.0)
            signal_multiplier = (confidence_multiplier + strength_multiplier) / 2
            signal_multiplier = max(0.5, min(1.0, signal_multiplier))

            # Market regime adjustment
            regime_multiplier = self._get_regime_multiplier(volatility_regime)

            # Calculate final position size
            position_size_usd = (self.current_balance * base_size_pct *
                               volatility_adjustment * signal_multiplier * regime_multiplier)

            # Ensure minimum trade size
            position_size_usd = max(position_size_usd, self.min_trade_size_usd)

            # Ensure we don't exceed available balance
            max_available = self.current_balance * 0.95  # Leave 5% buffer
            position_size_usd = min(position_size_usd, max_available)

            self.logger.info(f"Position sizing for {signal['asset']}: "
                           f"base=${self.current_balance * base_size_pct:.2f}, "
                           f"vol_adj={volatility_adjustment:.2f}, "
                           f"signal_mult={signal_multiplier:.2f}, "
                           f"regime_mult={regime_multiplier:.2f}, "
                           f"final=${position_size_usd:.2f}")

            return position_size_usd

        except Exception as e:
            self.logger.error(f"Volatility-based position sizing failed for {signal['asset']}: {e}")
            # Fallback to simple calculation
            return await self._fallback_position_size(signal)

    def _get_regime_multiplier(self, volatility_regime: str) -> float:
        """Get position size multiplier based on volatility regime"""
        regime_multipliers = {
            'low_volatility_stable': 1.2,      # Larger positions in stable low vol
            'low_volatility_expanding': 1.0,   # Normal size as vol might increase
            'low_volatility_contracting': 1.1, # Slightly larger as vol decreasing
            'normal_volatility_stable': 1.0,   # Normal positions
            'normal_volatility_expanding': 0.8, # Smaller as vol increasing
            'normal_volatility_contracting': 1.1, # Slightly larger as vol decreasing
            'elevated_volatility_stable': 0.7,  # Smaller positions in elevated vol
            'elevated_volatility_expanding': 0.5, # Much smaller as vol expanding
            'elevated_volatility_contracting': 0.8, # Smaller but vol decreasing
            'high_volatility_stable': 0.4,     # Very small positions in high vol
            'high_volatility_expanding': 0.3,  # Minimal positions, very risky
            'high_volatility_contracting': 0.5, # Small but vol decreasing
            'unknown_regime': 0.7              # Conservative default
        }

        return regime_multipliers.get(volatility_regime, 0.7)

    async def _fallback_position_size(self, signal: Dict[str, Any]) -> float:
        """Fallback position sizing when volatility analysis fails"""
        # Simple confidence-based sizing
        base_size_pct = self.max_position_size
        confidence_multiplier = signal.get('confidence', 0.5)
        strength_multiplier = signal.get('signal_strength', 0.5)

        risk_multiplier = (confidence_multiplier + strength_multiplier) / 2
        risk_multiplier = max(0.3, min(0.8, risk_multiplier))  # More conservative range

        position_size_usd = self.current_balance * base_size_pct * risk_multiplier
        position_size_usd = max(position_size_usd, self.min_trade_size_usd)

        max_available = self.current_balance * 0.95
        position_size_usd = min(position_size_usd, max_available)

        return position_size_usd

    def _calculate_slippage(self, market_data: Dict[str, Any], trade_size_usd: float) -> Dict[str, Any]:
        """Calculate realistic slippage based on liquidity and trade size"""
        # Get total liquidity from all sources
        total_liquidity = sum(
            source.get('liquidity_usd', 0)
            for source in market_data.get('sources', [])
        )

        if total_liquidity == 0:
            # Default slippage if no liquidity data
            return {
                'slippage_pct': self.base_slippage_bps / 10000,
                'liquidity_impact': 0
            }

        # Calculate liquidity impact
        liquidity_impact = (trade_size_usd / total_liquidity) * self.liquidity_impact_factor

        # Base slippage + liquidity impact
        total_slippage_pct = (self.base_slippage_bps / 10000) + liquidity_impact

        # Cap maximum slippage at 1%
        total_slippage_pct = min(total_slippage_pct, 0.01)

        return {
            'slippage_pct': total_slippage_pct,
            'liquidity_impact': liquidity_impact,
            'total_liquidity': total_liquidity
        }

    async def _estimate_gas_cost(self) -> float:
        """Estimate gas cost for trade execution"""
        try:
            # Get current gas prices
            gas_prices = await polygon_rpc.get_gas_price()
            gas_price_gwei = gas_prices.get('fast', 30)  # Use fast gas price

            # Estimate gas units for DEX swap (typically 150k-200k gas)
            estimated_gas_units = 175000

            # Calculate cost in USD (approximate)
            # MATIC price assumed ~$0.80, 1 Gwei = 1e-9 MATIC
            matic_price_usd = 0.80
            gas_cost_matic = (gas_price_gwei * estimated_gas_units) / 1e9
            gas_cost_usd = gas_cost_matic * matic_price_usd

            return gas_cost_usd

        except Exception as e:
            self.logger.warning(f"Gas estimation failed, using default: {e}")
            return self.gas_cost_usd

    def _create_position(self, signal: Dict[str, Any], execution_result: Dict[str, Any]) -> Position:
        """Create position object from signal and execution result"""
        position_type = PositionType.LONG if signal['signal_direction'] == 'BUY' else PositionType.SHORT

        return Position(
            asset=signal['asset'],
            position_type=position_type,
            entry_price=execution_result['fill_price'],
            quantity=execution_result['quantity'],
            entry_time=datetime.now(),
            stop_loss=signal.get('stop_loss'),
            target_price=signal.get('target_price'),
            max_hold_time=signal.get('max_hold_time'),
            current_price=execution_result['fill_price'],
            gas_cost=execution_result['gas_cost'],
            slippage_cost=execution_result['slippage_cost']
        )

    def _create_position_from_execution(self, signal: Dict[str, Any], execution_result: Dict[str, Any]) -> Position:
        """Create position object from enhanced signal and trading engine execution result"""
        position_type = PositionType.LONG if signal['signal_direction'] == 'BUY' else PositionType.SHORT

        return Position(
            asset=signal['asset'],
            position_type=position_type,
            entry_price=execution_result['execution_price'],
            quantity=execution_result['executed_quantity'],
            entry_time=datetime.now(),
            stop_loss=signal.get('stop_loss'),
            target_price=signal.get('target_price'),
            max_hold_time=signal.get('max_hold_time'),
            current_price=execution_result['execution_price'],
            gas_cost=execution_result['gas_cost'],
            slippage_cost=execution_result['slippage_cost']
        )

    async def _close_position(self, asset: str, exit_reason: str) -> Dict[str, Any]:
        """Close an open position"""
        try:
            position = self.positions.get(asset)
            if not position:
                return {'success': False, 'error': 'Position not found'}

            # Get current market price for exit
            exit_price = await self._get_current_price(asset)
            if exit_price is None:
                return {'success': False, 'error': 'Unable to get exit price'}

            # Calculate exit costs (gas + slippage)
            exit_gas_cost = await self._estimate_gas_cost()
            exit_slippage = self._calculate_exit_slippage(position, exit_price)

            # Apply slippage to exit price
            if position.position_type == PositionType.LONG:
                actual_exit_price = exit_price * (1 - exit_slippage['slippage_pct'])
            else:  # SHORT
                actual_exit_price = exit_price * (1 + exit_slippage['slippage_pct'])

            # Calculate realized PnL
            if position.position_type == PositionType.LONG:
                gross_pnl = (actual_exit_price - position.entry_price) * position.quantity
            else:  # SHORT
                gross_pnl = (position.entry_price - actual_exit_price) * position.quantity

            # Subtract all costs
            total_costs = position.gas_cost + position.slippage_cost + exit_gas_cost + exit_slippage['cost']
            realized_pnl = gross_pnl - total_costs

            # Create trade execution record
            trade_execution = TradeExecution(
                trade_id=self._generate_trade_id(),
                asset=asset,
                signal_direction=position.position_type.value,
                entry_price=position.entry_price,
                exit_price=actual_exit_price,
                quantity=position.quantity,
                entry_time=position.entry_time,
                exit_time=datetime.now(),
                realized_pnl=realized_pnl,
                gas_cost=position.gas_cost + exit_gas_cost,
                slippage_cost=position.slippage_cost + exit_slippage['cost'],
                exit_reason=exit_reason,
                hold_time_minutes=(datetime.now() - position.entry_time).total_seconds() / 60
            )

            # Update portfolio
            self.current_balance += (position.quantity * actual_exit_price) + realized_pnl
            self.trade_history.append(trade_execution)
            self.total_trades += 1
            self.total_pnl += realized_pnl
            self.total_fees += total_costs

            if realized_pnl > 0:
                self.winning_trades += 1

            # Remove position
            del self.positions[asset]

            self.logger.info(f"Position closed: {asset} {exit_reason} PnL=${realized_pnl:.2f}")

            return {
                'success': True,
                'realized_pnl': realized_pnl,
                'exit_price': actual_exit_price,
                'trade_execution': trade_execution
            }

        except Exception as e:
            self.logger.error(f"Failed to close position {asset}: {e}")
            return {'success': False, 'error': str(e)}

    def _calculate_exit_slippage(self, position: Position, exit_price: float) -> Dict[str, Any]:
        """Calculate slippage for position exit"""
        # Simplified exit slippage calculation
        position_value = position.quantity * exit_price
        base_slippage_pct = self.base_slippage_bps / 10000

        # Assume similar liquidity impact as entry
        slippage_cost = position_value * base_slippage_pct

        return {
            'slippage_pct': base_slippage_pct,
            'cost': slippage_cost
        }

    async def _get_current_price(self, asset: str) -> Optional[float]:
        """Get current market price for asset"""
        try:
            token_address = self._extract_token_address(asset)
            price_data = await price_aggregator.get_token_price(token_address, "USDC")

            if price_data and 'price' in price_data:
                return price_data['price']

            return None

        except Exception as e:
            self.logger.error(f"Failed to get current price for {asset}: {e}")
            return None

    def _extract_token_address(self, asset: str) -> str:
        """Extract token address from asset symbol"""
        # This is a simplified implementation
        # In practice, you'd have a mapping of symbols to addresses
        # For now, return a placeholder that would work with the existing system

        # Common Polygon token addresses (examples)
        token_map = {
            'MATIC/USDC': '******************************************',  # WMATIC
            'WETH/USDC': '******************************************',  # WETH
            'USDT/USDC': '******************************************',  # USDT
        }

        return token_map.get(asset, '******************************************')  # Default to WMATIC

    def _generate_trade_id(self) -> str:
        """Generate unique trade ID"""
        import uuid
        return f"trade_{uuid.uuid4().hex[:8]}"

    def get_portfolio_summary(self) -> Dict[str, Any]:
        """Get comprehensive portfolio summary"""
        # Calculate current portfolio value
        total_position_value = 0
        unrealized_pnl = 0

        for position in self.positions.values():
            position_value = position.quantity * position.current_price
            total_position_value += position_value
            unrealized_pnl += position.unrealized_pnl

        total_portfolio_value = self.current_balance + total_position_value

        # Calculate performance metrics
        total_return = total_portfolio_value - self.initial_balance
        total_return_pct = (total_return / self.initial_balance) * 100

        win_rate = (self.winning_trades / self.total_trades * 100) if self.total_trades > 0 else 0

        avg_trade_pnl = self.total_pnl / self.total_trades if self.total_trades > 0 else 0
        avg_winning_trade = 0
        avg_losing_trade = 0

        if self.trade_history:
            winning_trades = [t.realized_pnl for t in self.trade_history if t.realized_pnl > 0]
            losing_trades = [t.realized_pnl for t in self.trade_history if t.realized_pnl <= 0]

            avg_winning_trade = sum(winning_trades) / len(winning_trades) if winning_trades else 0
            avg_losing_trade = sum(losing_trades) / len(losing_trades) if losing_trades else 0

        return {
            'portfolio_value': {
                'initial_balance': self.initial_balance,
                'current_balance': self.current_balance,
                'position_value': total_position_value,
                'total_value': total_portfolio_value,
                'unrealized_pnl': unrealized_pnl,
                'realized_pnl': self.total_pnl
            },
            'performance': {
                'total_return': total_return,
                'total_return_pct': total_return_pct,
                'total_trades': self.total_trades,
                'winning_trades': self.winning_trades,
                'losing_trades': self.total_trades - self.winning_trades,
                'win_rate_pct': win_rate,
                'avg_trade_pnl': avg_trade_pnl,
                'avg_winning_trade': avg_winning_trade,
                'avg_losing_trade': avg_losing_trade,
                'total_fees': self.total_fees
            },
            'positions': {
                'open_positions': len(self.positions),
                'position_details': [
                    {
                        'asset': pos.asset,
                        'type': pos.position_type.value,
                        'entry_price': pos.entry_price,
                        'current_price': pos.current_price,
                        'quantity': pos.quantity,
                        'unrealized_pnl': pos.unrealized_pnl,
                        'hold_time_minutes': (datetime.now() - pos.entry_time).total_seconds() / 60
                    }
                    for pos in self.positions.values()
                ]
            },
            'risk_metrics': {
                'max_position_size_pct': self.max_position_size * 100,
                'max_total_exposure_pct': self.max_total_exposure * 100,
                'current_exposure_pct': (total_position_value / self.initial_balance) * 100,
                'available_balance': self.current_balance,
                'balance_utilization_pct': ((self.initial_balance - self.current_balance) / self.initial_balance) * 100
            }
        }

    def get_trade_history(self, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """Get trade history with optional limit"""
        trades = self.trade_history[-limit:] if limit else self.trade_history

        return [
            {
                'trade_id': trade.trade_id,
                'asset': trade.asset,
                'direction': trade.signal_direction,
                'entry_price': trade.entry_price,
                'exit_price': trade.exit_price,
                'quantity': trade.quantity,
                'entry_time': trade.entry_time.isoformat(),
                'exit_time': trade.exit_time.isoformat(),
                'hold_time_minutes': trade.hold_time_minutes,
                'realized_pnl': trade.realized_pnl,
                'gas_cost': trade.gas_cost,
                'slippage_cost': trade.slippage_cost,
                'exit_reason': trade.exit_reason,
                'success': trade.success
            }
            for trade in trades
        ]

    async def close_all_positions(self, reason: str = "manual_close") -> Dict[str, Any]:
        """Close all open positions"""
        results = []

        for asset in list(self.positions.keys()):
            close_result = await self._close_position(asset, reason)
            results.append({
                'asset': asset,
                'success': close_result['success'],
                'pnl': close_result.get('realized_pnl', 0),
                'error': close_result.get('error')
            })

        return {
            'closed_positions': len(results),
            'results': results,
            'total_pnl': sum(r['pnl'] for r in results if r['success'])
        }

    def reset_portfolio(self):
        """Reset portfolio to initial state"""
        self.current_balance = self.initial_balance
        self.positions.clear()
        self.trade_history.clear()
        self.total_trades = 0
        self.winning_trades = 0
        self.total_pnl = 0.0
        self.total_fees = 0.0

        self.logger.info("Portfolio reset to initial state")

    def export_trade_history_csv(self, filename: str = None) -> str:
        """Export trade history to CSV format"""
        if not filename:
            filename = f"trade_history_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"

        import csv
        import os

        # Ensure logs directory exists
        os.makedirs('logs', exist_ok=True)
        filepath = os.path.join('logs', filename)

        with open(filepath, 'w', newline='') as csvfile:
            fieldnames = [
                'trade_id', 'asset', 'direction', 'entry_price', 'exit_price',
                'quantity', 'entry_time', 'exit_time', 'hold_time_minutes',
                'realized_pnl', 'gas_cost', 'slippage_cost', 'exit_reason', 'success'
            ]

            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()

            for trade in self.trade_history:
                writer.writerow({
                    'trade_id': trade.trade_id,
                    'asset': trade.asset,
                    'direction': trade.signal_direction,
                    'entry_price': trade.entry_price,
                    'exit_price': trade.exit_price,
                    'quantity': trade.quantity,
                    'entry_time': trade.entry_time.isoformat(),
                    'exit_time': trade.exit_time.isoformat(),
                    'hold_time_minutes': trade.hold_time_minutes,
                    'realized_pnl': trade.realized_pnl,
                    'gas_cost': trade.gas_cost,
                    'slippage_cost': trade.slippage_cost,
                    'exit_reason': trade.exit_reason,
                    'success': trade.success
                })

        self.logger.info(f"Trade history exported to {filepath}")
        return filepath
