"""
Advanced Risk Management System - Phase 4 Implementation
Dynamic risk controls, position sizing, and exposure management
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import statistics
import math

from config.settings import Config
from ..market_intelligence.probability_engine import probability_engine


class RiskLevel(Enum):
    """Risk level classifications"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    EXTREME = "extreme"


@dataclass
class RiskAssessment:
    """Risk assessment result"""
    risk_level: RiskLevel
    risk_score: float  # 0-100
    position_size_multiplier: float  # 0.0-1.0
    recommended_stop_loss: Optional[float]
    recommended_take_profit: Optional[float]
    max_hold_time: Optional[int]  # minutes
    warnings: List[str]
    reasoning: str


class RiskManager:
    """Advanced risk management system"""
    
    def __init__(self, config: Config):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Risk parameters
        self.max_portfolio_risk_pct = 0.02  # 2% max portfolio risk per trade
        self.max_daily_loss_pct = 0.05  # 5% max daily loss
        self.max_drawdown_pct = 0.15  # 15% max drawdown before stopping
        
        # Position sizing parameters
        self.base_position_size_pct = 0.05  # 5% base position size
        self.max_position_size_pct = 0.15  # 15% max position size
        self.min_position_size_usd = 50  # $50 minimum position
        
        # Stop loss parameters
        self.default_stop_loss_pct = 0.02  # 2% default stop loss
        self.max_stop_loss_pct = 0.05  # 5% maximum stop loss
        self.trailing_stop_activation = 0.015  # 1.5% profit before trailing
        
        # Volatility-based adjustments
        self.volatility_lookback_hours = 24
        self.high_volatility_threshold = 0.03  # 3% hourly volatility
        
        # Correlation limits
        self.max_correlated_positions = 3
        self.correlation_threshold = 0.7
        
    async def assess_trade_risk(
        self,
        signal: Dict[str, Any],
        portfolio_state: Dict[str, Any],
        market_conditions: Dict[str, Any]
    ) -> RiskAssessment:
        """Comprehensive risk assessment for a trade signal with probability-based enhancement"""

        try:
            risk_factors = []
            warnings = []

            # Phase 2 Enhancement: Add probability-based risk assessment
            probability_risk = await self._assess_probability_risk(signal, market_conditions)
            risk_factors.append(probability_risk)

            # 1. Signal quality risk (enhanced with probability analysis)
            signal_risk = self._assess_signal_risk(signal, probability_risk)
            risk_factors.append(signal_risk)

            # 2. Portfolio risk
            portfolio_risk = self._assess_portfolio_risk(signal, portfolio_state)
            risk_factors.append(portfolio_risk)

            # 3. Market condition risk (enhanced with volatility regime analysis)
            market_risk = await self._assess_market_risk_enhanced(signal, market_conditions)
            risk_factors.append(market_risk)

            # 4. Correlation risk (enhanced with real correlation data)
            correlation_risk = await self._assess_correlation_risk_enhanced(signal, portfolio_state)
            risk_factors.append(correlation_risk)

            # 5. Timing risk
            timing_risk = self._assess_timing_risk(signal)
            risk_factors.append(timing_risk)

            # Calculate overall risk score with probability weighting
            risk_score = self._calculate_overall_risk_enhanced(risk_factors, probability_risk)
            risk_level = self._determine_risk_level(risk_score)

            # Calculate position size multiplier with probability-based adjustment
            position_multiplier = self._calculate_position_multiplier_enhanced(
                risk_score, signal, probability_risk
            )

            # Generate stop loss and take profit recommendations
            stop_loss = self._calculate_dynamic_stop_loss(signal, risk_score, market_conditions)
            take_profit = self._calculate_take_profit(signal, risk_score)

            # Calculate max hold time
            max_hold_time = self._calculate_max_hold_time(signal, risk_score)

            # Collect warnings (enhanced with probability warnings)
            warnings.extend(self._generate_risk_warnings_enhanced(risk_factors, risk_score, probability_risk))

            # Generate reasoning (enhanced with probability reasoning)
            reasoning = self._generate_risk_reasoning_enhanced(risk_factors, risk_score, probability_risk)

            return RiskAssessment(
                risk_level=risk_level,
                risk_score=risk_score,
                position_size_multiplier=position_multiplier,
                recommended_stop_loss=stop_loss,
                recommended_take_profit=take_profit,
                max_hold_time=max_hold_time,
                warnings=warnings,
                reasoning=reasoning
            )

        except Exception as e:
            self.logger.error(f"Risk assessment failed: {e}")
            return self._conservative_risk_assessment()

    async def _assess_probability_risk(self, signal: Dict[str, Any], market_conditions: Dict[str, Any]) -> Dict[str, Any]:
        """Phase 2: Assess risk using probability-based analysis"""
        try:
            token_address = signal.get('token_address', '')
            if not token_address:
                return self._get_default_probability_risk()

            # Get probability comparison from Phase 2 engine
            probability_comparison = await probability_engine.compare_market_vs_ai_probabilities(
                token_address, signal
            )

            # Extract key probability metrics
            combined_edge = probability_comparison.get('combined_probability_edge', {})
            opportunity_analysis = probability_comparison.get('opportunity_analysis', {})
            trading_recommendations = probability_comparison.get('trading_recommendations', {})

            # Calculate probability-based risk score
            edge_strength = combined_edge.get('edge_strength', 'negligible')
            overall_edge = combined_edge.get('overall_edge', 0)
            overall_confidence = combined_edge.get('overall_confidence', 0.5)
            opportunity_score = opportunity_analysis.get('opportunity_score', 0)

            # Convert probability metrics to risk score (inverse relationship)
            # Strong positive edge = lower risk, strong negative edge = higher risk
            if edge_strength == 'strong' and overall_edge > 0:
                probability_risk_score = 5  # Very low risk
            elif edge_strength == 'moderate' and overall_edge > 0:
                probability_risk_score = 15  # Low risk
            elif edge_strength == 'weak' or overall_edge == 0:
                probability_risk_score = 25  # Medium risk
            elif overall_edge < 0:
                probability_risk_score = 40  # High risk - market disagrees with AI
            else:
                probability_risk_score = 30  # Default medium-high risk

            # Adjust for confidence level
            confidence_adjustment = (0.5 - overall_confidence) * 20  # Lower confidence = higher risk
            probability_risk_score += confidence_adjustment

            # Adjust for opportunity quality
            opportunity_adjustment = (0.5 - opportunity_score) * 15  # Lower opportunity = higher risk
            probability_risk_score += opportunity_adjustment

            # Cap the risk score
            probability_risk_score = max(0, min(probability_risk_score, 50))

            return {
                'category': 'probability_assessment',
                'risk_score': probability_risk_score,
                'factors': {
                    'edge_strength': edge_strength,
                    'overall_edge': overall_edge,
                    'overall_confidence': overall_confidence,
                    'opportunity_score': opportunity_score,
                    'market_vs_ai_agreement': 'agree' if abs(overall_edge) < 0.05 else 'disagree'
                },
                'probability_comparison': probability_comparison,
                'trading_recommendations': trading_recommendations
            }

        except Exception as e:
            self.logger.error(f"Probability risk assessment failed: {e}")
            return self._get_default_probability_risk()

    def _get_default_probability_risk(self) -> Dict[str, Any]:
        """Return default probability risk when assessment fails"""
        return {
            'category': 'probability_assessment',
            'risk_score': 30,  # Medium risk default
            'factors': {
                'edge_strength': 'unknown',
                'overall_edge': 0,
                'overall_confidence': 0.5,
                'opportunity_score': 0,
                'market_vs_ai_agreement': 'unknown'
            },
            'probability_comparison': {},
            'trading_recommendations': {
                'trade_recommendation': 'HOLD',
                'confidence_level': 'LOW',
                'position_size_multiplier': 0.5
            }
        }
    
    def _assess_signal_risk(self, signal: Dict[str, Any], probability_risk: Dict[str, Any] = None) -> Dict[str, Any]:
        """Assess risk based on signal quality - enhanced with probability analysis"""
        confidence = signal.get('confidence', 0.5)
        signal_strength = signal.get('signal_strength', 0.5)

        # Lower confidence/strength = higher risk
        confidence_risk = (1 - confidence) * 30  # 0-30 points
        strength_risk = (1 - signal_strength) * 20  # 0-20 points

        # Check for conflicting signals
        lstm_predictions = signal.get('lstm_predictions', {})
        conflicting_signals = 0

        if lstm_predictions:
            directions = []
            for timeframe, pred in lstm_predictions.items():
                if pred and pred.get('predicted_change'):
                    directions.append(1 if pred['predicted_change'] > 0 else -1)

            if len(set(directions)) > 1:  # Conflicting directions
                conflicting_signals = 15

        # Phase 2 Enhancement: Adjust for probability analysis
        probability_adjustment = 0
        if probability_risk:
            market_vs_ai_agreement = probability_risk.get('factors', {}).get('market_vs_ai_agreement', 'unknown')
            if market_vs_ai_agreement == 'disagree':
                probability_adjustment = 10  # Higher risk when market and AI disagree
            elif market_vs_ai_agreement == 'agree':
                probability_adjustment = -5  # Lower risk when they agree

        total_risk = confidence_risk + strength_risk + conflicting_signals + probability_adjustment

        return {
            'category': 'signal_quality',
            'risk_score': min(max(total_risk, 0), 50),  # Cap between 0-50
            'factors': {
                'confidence_risk': confidence_risk,
                'strength_risk': strength_risk,
                'conflicting_signals': conflicting_signals,
                'probability_adjustment': probability_adjustment
            }
        }
    
    def _assess_portfolio_risk(self, signal: Dict[str, Any], portfolio_state: Dict[str, Any]) -> Dict[str, Any]:
        """Assess portfolio-level risk"""
        current_positions = len(portfolio_state.get('positions', {}).get('position_details', []))
        total_exposure_pct = portfolio_state.get('risk_metrics', {}).get('current_exposure_pct', 0)
        
        # Position concentration risk
        concentration_risk = 0
        if current_positions >= 5:
            concentration_risk = 10
        elif current_positions >= 8:
            concentration_risk = 20
        
        # Exposure risk
        exposure_risk = 0
        if total_exposure_pct > 70:
            exposure_risk = 15
        elif total_exposure_pct > 50:
            exposure_risk = 10
        
        # Check for existing position in same asset
        asset = signal.get('asset', '')
        existing_position_risk = 0
        for pos in portfolio_state.get('positions', {}).get('position_details', []):
            if pos.get('asset') == asset:
                existing_position_risk = 25  # High risk for duplicate positions
                break
        
        total_risk = concentration_risk + exposure_risk + existing_position_risk
        
        return {
            'category': 'portfolio',
            'risk_score': min(total_risk, 50),
            'factors': {
                'concentration_risk': concentration_risk,
                'exposure_risk': exposure_risk,
                'existing_position_risk': existing_position_risk
            }
        }

    async def _assess_market_risk_enhanced(self, signal: Dict[str, Any], market_conditions: Dict[str, Any]) -> Dict[str, Any]:
        """Phase 2: Enhanced market condition risk assessment with volatility regime analysis"""
        try:
            # Get basic market risk assessment
            basic_market_risk = self._assess_market_risk(signal, market_conditions)
            base_risk_score = basic_market_risk['risk_score']

            # Get volatility regime analysis from Phase 1
            token_address = signal.get('token_address', '')
            if token_address:
                from ..market_intelligence.volatility_analyzer import volatility_analyzer
                volatility_analysis = await volatility_analyzer.analyze_asset_volatility(token_address)

                # Adjust risk based on volatility regime
                volatility_regime = volatility_analysis.get('volatility_regime', 'unknown')
                regime_adjustment = self._calculate_regime_risk_adjustment(volatility_regime)

                # Adjust for market efficiency
                market_expectations = volatility_analysis.get('market_expectations', {})
                efficiency_assessment = market_expectations.get('efficiency_assessment', 'unknown')
                efficiency_adjustment = self._calculate_efficiency_risk_adjustment(efficiency_assessment)

                # Combine adjustments
                total_adjustment = regime_adjustment + efficiency_adjustment
                adjusted_risk_score = base_risk_score + total_adjustment

                # Update factors
                enhanced_factors = basic_market_risk['factors'].copy()
                enhanced_factors.update({
                    'volatility_regime': volatility_regime,
                    'regime_adjustment': regime_adjustment,
                    'efficiency_assessment': efficiency_assessment,
                    'efficiency_adjustment': efficiency_adjustment,
                    'volatility_analysis_quality': volatility_analysis.get('analysis_quality', {}).get('quality_level', 'unknown')
                })

                return {
                    'category': 'market_conditions_enhanced',
                    'risk_score': min(max(adjusted_risk_score, 0), 70),  # Cap between 0-70
                    'factors': enhanced_factors
                }
            else:
                # Fallback to basic assessment if no token address
                return basic_market_risk

        except Exception as e:
            self.logger.error(f"Enhanced market risk assessment failed: {e}")
            return self._assess_market_risk(signal, market_conditions)

    def _calculate_regime_risk_adjustment(self, volatility_regime: str) -> float:
        """Calculate risk adjustment based on volatility regime"""
        if 'high_volatility' in volatility_regime:
            if 'expanding' in volatility_regime:
                return 15  # High risk - volatility expanding in high regime
            else:
                return 8   # Medium-high risk - high but stable/contracting
        elif 'elevated_volatility' in volatility_regime:
            if 'expanding' in volatility_regime:
                return 8   # Medium risk - volatility expanding
            else:
                return 3   # Low-medium risk
        elif 'low_volatility' in volatility_regime:
            if 'expanding' in volatility_regime:
                return -5  # Lower risk - volatility expanding from low base
            else:
                return 5   # Medium risk - low volatility can be risky (low activity)
        else:
            return 0  # No adjustment for unknown/normal regimes

    def _calculate_efficiency_risk_adjustment(self, efficiency_assessment: str) -> float:
        """Calculate risk adjustment based on market efficiency"""
        if efficiency_assessment == 'underpriced_volatility':
            return -8  # Lower risk - market underpricing volatility (opportunity)
        elif efficiency_assessment == 'overpriced_volatility':
            return 5   # Higher risk - market overpricing volatility
        elif efficiency_assessment == 'efficient':
            return 2   # Slightly higher risk - harder to find edges
        else:
            return 0   # No adjustment for unknown efficiency
    
    def _assess_market_risk(self, signal: Dict[str, Any], market_conditions: Dict[str, Any]) -> Dict[str, Any]:
        """Assess market condition risk - adaptive to real conditions"""
        # Get market data with defaults
        volatility = market_conditions.get('volatility', 0.02)
        volume_24h = market_conditions.get('volume_24h', 1000000)
        price_change_24h = abs(market_conditions.get('price_change_24h', 0.0))
        liquidity_score = market_conditions.get('liquidity_score', 0.7)

        # Volatility risk - more nuanced assessment
        volatility_risk = 0
        if volatility > 0.15:  # Very high volatility
            volatility_risk = 25
        elif volatility > 0.08:  # High volatility
            volatility_risk = 15
        elif volatility > 0.04:  # Medium volatility
            volatility_risk = 8
        elif volatility < 0.005:  # Very low volatility (also risky - low activity)
            volatility_risk = 10

        # Volume/Liquidity risk - flow with market activity
        volume_risk = 0
        if volume_24h < 100000:  # Very low volume
            volume_risk = 20
        elif volume_24h < 500000:  # Low volume
            volume_risk = 12
        elif volume_24h > 10000000:  # Very high volume (potential manipulation)
            volume_risk = 8

        # Price movement risk - recent volatility
        price_movement_risk = 0
        if price_change_24h > 0.2:  # >20% change
            price_movement_risk = 20
        elif price_change_24h > 0.1:  # >10% change
            price_movement_risk = 12
        elif price_change_24h > 0.05:  # >5% change
            price_movement_risk = 6

        # Liquidity score risk
        liquidity_risk = max(0, (0.5 - liquidity_score) * 40)  # Scale 0.5-0 to 0-20 risk

        total_risk = volatility_risk + volume_risk + price_movement_risk + liquidity_risk

        return {
            'category': 'market_conditions',
            'risk_score': min(total_risk, 60),  # Allow higher market risk scores
            'factors': {
                'volatility_risk': volatility_risk,
                'volume_risk': volume_risk,
                'price_movement_risk': price_movement_risk,
                'liquidity_risk': liquidity_risk
            }
        }
    
    def _assess_correlation_risk(self, signal: Dict[str, Any], portfolio_state: Dict[str, Any]) -> Dict[str, Any]:
        """Assess correlation risk with existing positions"""
        # Simplified correlation assessment
        # In practice, this would use actual price correlation data
        
        asset = signal.get('asset', '')
        existing_assets = [
            pos.get('asset', '') 
            for pos in portfolio_state.get('positions', {}).get('position_details', [])
        ]
        
        correlation_risk = 0
        
        # Simple asset correlation heuristics
        if 'MATIC' in asset:
            correlated_count = sum(1 for a in existing_assets if 'MATIC' in a or 'WETH' in a)
        elif 'WETH' in asset:
            correlated_count = sum(1 for a in existing_assets if 'WETH' in a or 'MATIC' in a)
        else:
            correlated_count = sum(1 for a in existing_assets if a.split('/')[0] == asset.split('/')[0])
        
        if correlated_count >= 2:
            correlation_risk = 20
        elif correlated_count >= 1:
            correlation_risk = 10
        
        return {
            'category': 'correlation',
            'risk_score': correlation_risk,
            'factors': {
                'correlated_positions': correlated_count
            }
        }

    async def _assess_correlation_risk_enhanced(self, signal: Dict[str, Any], portfolio_state: Dict[str, Any]) -> Dict[str, Any]:
        """Phase 2: Enhanced correlation risk assessment with real correlation data"""
        try:
            asset = signal.get('asset', '')
            token_address = signal.get('token_address', '')
            existing_positions = portfolio_state.get('positions', {}).get('position_details', [])

            if not existing_positions or not token_address:
                return self._assess_correlation_risk(signal, portfolio_state)

            # Calculate real price correlations using historical data
            correlation_data = await self._calculate_real_correlations(token_address, existing_positions)

            # Base correlation risk from traditional assessment
            basic_correlation_risk = self._assess_correlation_risk(signal, portfolio_state)
            base_risk = basic_correlation_risk['risk_score']

            # Enhanced correlation analysis
            high_correlation_count = 0
            moderate_correlation_count = 0
            correlation_details = []

            for correlation_info in correlation_data:
                correlation_coeff = correlation_info.get('correlation', 0)
                existing_asset = correlation_info.get('asset', '')

                if abs(correlation_coeff) > 0.7:  # High correlation
                    high_correlation_count += 1
                    correlation_details.append(f"{existing_asset}: {correlation_coeff:.2f}")
                elif abs(correlation_coeff) > 0.4:  # Moderate correlation
                    moderate_correlation_count += 1
                    correlation_details.append(f"{existing_asset}: {correlation_coeff:.2f}")

            # Calculate enhanced correlation risk
            high_correlation_risk = high_correlation_count * 15  # 15 points per high correlation
            moderate_correlation_risk = moderate_correlation_count * 8  # 8 points per moderate correlation

            # Add volatility correlation risk
            volatility_correlation_risk = await self._calculate_volatility_correlation_risk(
                token_address, existing_positions
            )

            # Sector/category correlation risk
            sector_correlation_risk = self._calculate_sector_correlation_risk(asset, existing_positions)

            total_enhanced_risk = (
                high_correlation_risk +
                moderate_correlation_risk +
                volatility_correlation_risk +
                sector_correlation_risk
            )

            # Combine with base risk (weighted average)
            final_risk = (base_risk * 0.3) + (total_enhanced_risk * 0.7)

            enhanced_factors = {
                'high_correlation_count': high_correlation_count,
                'moderate_correlation_count': moderate_correlation_count,
                'high_correlation_risk': high_correlation_risk,
                'moderate_correlation_risk': moderate_correlation_risk,
                'volatility_correlation_risk': volatility_correlation_risk,
                'sector_correlation_risk': sector_correlation_risk,
                'correlation_details': correlation_details,
                'total_positions': len(existing_positions),
                'correlation_analysis_method': 'real_historical_data'
            }

            return {
                'category': 'correlation_enhanced',
                'risk_score': min(final_risk, 40),  # Cap at 40
                'factors': enhanced_factors
            }

        except Exception as e:
            self.logger.error(f"Enhanced correlation risk assessment failed: {e}")
            return self._assess_correlation_risk(signal, portfolio_state)

    async def _calculate_real_correlations(self, token_address: str, existing_positions: List[Dict]) -> List[Dict]:
        """Calculate real price correlations using historical data"""
        try:
            from ..data_collectors.price_aggregator import price_aggregator

            correlations = []

            # Get historical prices for the target asset
            target_prices = await price_aggregator.get_historical_prices(
                token_address, "USDC", 168, interval="1h"  # 7 days of hourly data
            )

            if len(target_prices) < 24:  # Need at least 24 hours of data
                return []

            target_returns = self._calculate_returns(target_prices)

            # Calculate correlations with existing positions
            for position in existing_positions:
                existing_token_address = position.get('token_address', '')
                existing_asset = position.get('asset', '')

                if existing_token_address and existing_token_address != token_address:
                    # Get historical prices for existing position
                    existing_prices = await price_aggregator.get_historical_prices(
                        existing_token_address, "USDC", 168, interval="1h"
                    )

                    if len(existing_prices) >= 24:
                        existing_returns = self._calculate_returns(existing_prices)

                        # Calculate correlation coefficient
                        correlation = self._calculate_correlation_coefficient(target_returns, existing_returns)

                        correlations.append({
                            'asset': existing_asset,
                            'token_address': existing_token_address,
                            'correlation': correlation,
                            'data_points': min(len(target_returns), len(existing_returns))
                        })

            return correlations

        except Exception as e:
            self.logger.error(f"Real correlation calculation failed: {e}")
            return []

    def _calculate_returns(self, price_data: List[Dict]) -> List[float]:
        """Calculate price returns from historical price data"""
        returns = []
        for i in range(1, len(price_data)):
            prev_price = price_data[i-1].get('price', 0)
            curr_price = price_data[i].get('price', 0)

            if prev_price > 0 and curr_price > 0:
                return_pct = (curr_price - prev_price) / prev_price
                returns.append(return_pct)

        return returns

    def _calculate_correlation_coefficient(self, returns1: List[float], returns2: List[float]) -> float:
        """Calculate Pearson correlation coefficient between two return series"""
        try:
            # Align the series to the same length
            min_length = min(len(returns1), len(returns2))
            if min_length < 10:  # Need at least 10 data points
                return 0.0

            r1 = returns1[:min_length]
            r2 = returns2[:min_length]

            # Calculate means
            mean1 = sum(r1) / len(r1)
            mean2 = sum(r2) / len(r2)

            # Calculate correlation coefficient
            numerator = sum((x - mean1) * (y - mean2) for x, y in zip(r1, r2))

            sum_sq1 = sum((x - mean1) ** 2 for x in r1)
            sum_sq2 = sum((y - mean2) ** 2 for y in r2)

            denominator = math.sqrt(sum_sq1 * sum_sq2)

            return numerator / denominator if denominator > 0 else 0.0

        except Exception as e:
            self.logger.error(f"Correlation coefficient calculation failed: {e}")
            return 0.0

    async def _calculate_volatility_correlation_risk(self, token_address: str, existing_positions: List[Dict]) -> float:
        """Calculate risk from volatility correlations"""
        try:
            # This would analyze how volatilities of different assets correlate
            # For now, use a simplified approach based on position count and types

            if len(existing_positions) >= 4:
                return 8  # Higher risk with many positions
            elif len(existing_positions) >= 2:
                return 4  # Moderate risk
            else:
                return 0  # Low risk

        except Exception:
            return 0

    def _calculate_sector_correlation_risk(self, asset: str, existing_positions: List[Dict]) -> float:
        """Calculate risk from sector/category correlations"""
        try:
            # Simple sector classification based on asset names
            asset_sectors = {
                'WETH': 'ethereum_ecosystem',
                'MATIC': 'layer2_scaling',
                'USDC': 'stablecoin',
                'USDT': 'stablecoin',
                'WBTC': 'bitcoin_ecosystem'
            }

            current_sector = None
            for token in asset_sectors:
                if token in asset.upper():
                    current_sector = asset_sectors[token]
                    break

            if not current_sector:
                return 0  # Unknown sector, no additional risk

            # Count positions in the same sector
            same_sector_count = 0
            for position in existing_positions:
                position_asset = position.get('asset', '').upper()
                for token in asset_sectors:
                    if token in position_asset and asset_sectors[token] == current_sector:
                        same_sector_count += 1
                        break

            # Calculate sector concentration risk
            return same_sector_count * 5  # 5 points per same-sector position

        except Exception:
            return 0
    
    def _assess_timing_risk(self, signal: Dict[str, Any]) -> Dict[str, Any]:
        """Assess timing-based risk factors"""
        current_time = datetime.now()
        
        # Weekend risk (crypto markets are 24/7 but lower liquidity on weekends)
        weekend_risk = 0
        if current_time.weekday() >= 5:  # Saturday or Sunday
            weekend_risk = 10
        
        # Late night/early morning risk (lower liquidity)
        hour_risk = 0
        if current_time.hour < 6 or current_time.hour > 22:
            hour_risk = 5
        
        # Signal age risk
        signal_timestamp = signal.get('timestamp')
        age_risk = 0
        if signal_timestamp:
            try:
                signal_time = datetime.fromisoformat(signal_timestamp.replace('Z', '+00:00'))
                age_minutes = (current_time - signal_time.replace(tzinfo=None)).total_seconds() / 60
                if age_minutes > 5:  # Signal older than 5 minutes
                    age_risk = min(age_minutes * 2, 20)
            except:
                age_risk = 10  # Unknown age = moderate risk
        
        total_risk = weekend_risk + hour_risk + age_risk
        
        return {
            'category': 'timing',
            'risk_score': min(total_risk, 30),
            'factors': {
                'weekend_risk': weekend_risk,
                'hour_risk': hour_risk,
                'age_risk': age_risk
            }
        }
    
    def _calculate_overall_risk(self, risk_factors: List[Dict[str, Any]]) -> float:
        """Calculate overall risk score from individual factors"""
        total_risk = sum(factor['risk_score'] for factor in risk_factors)
        
        # Apply non-linear scaling to emphasize high-risk combinations
        if total_risk > 100:
            total_risk = 100 + math.sqrt(total_risk - 100) * 10
        
        return min(total_risk, 200)  # Cap at 200

    def _calculate_overall_risk_enhanced(self, risk_factors: List[Dict[str, Any]], probability_risk: Dict[str, Any]) -> float:
        """Phase 2: Enhanced overall risk calculation with probability weighting"""
        try:
            # Separate probability risk from other factors
            other_factors = [f for f in risk_factors if f['category'] != 'probability_assessment']

            # Calculate base risk from traditional factors
            base_risk = sum(factor['risk_score'] for factor in other_factors)

            # Get probability risk score
            prob_risk_score = probability_risk.get('risk_score', 30)

            # Weight probability risk more heavily (it's our edge)
            probability_weight = 0.4  # 40% weight to probability assessment
            traditional_weight = 0.6  # 60% weight to traditional factors

            # Calculate weighted risk
            weighted_risk = (base_risk * traditional_weight) + (prob_risk_score * probability_weight)

            # Apply non-linear scaling for high-risk combinations
            if weighted_risk > 100:
                weighted_risk = 100 + math.sqrt(weighted_risk - 100) * 10

            return min(weighted_risk, 200)  # Cap at 200

        except Exception as e:
            self.logger.error(f"Enhanced risk calculation failed: {e}")
            return self._calculate_overall_risk(risk_factors)
    
    def _determine_risk_level(self, risk_score: float) -> RiskLevel:
        """Determine risk level from risk score - more balanced thresholds"""
        if risk_score <= 25:
            return RiskLevel.LOW
        elif risk_score <= 55:
            return RiskLevel.MEDIUM
        elif risk_score <= 85:
            return RiskLevel.HIGH
        else:
            return RiskLevel.EXTREME
    
    def _calculate_position_multiplier(self, risk_score: float, signal: Dict[str, Any]) -> float:
        """Calculate adaptive position size multiplier - flow with market conditions"""
        # More adaptive base multiplier - less conservative, more responsive
        if risk_score <= 20:
            base_multiplier = 1.0  # Full position for very low risk
        elif risk_score <= 40:
            base_multiplier = 0.85  # Strong position for low-medium risk
        elif risk_score <= 60:
            base_multiplier = 0.7   # Moderate position for medium risk
        elif risk_score <= 80:
            base_multiplier = 0.5   # Reduced position for high risk
        elif risk_score <= 100:
            base_multiplier = 0.3   # Small position for very high risk
        else:
            base_multiplier = 0.15  # Minimal position for extreme risk

        # Enhance with signal quality - reward strong signals
        confidence = signal.get('confidence', 0.5)
        signal_strength = signal.get('signal_strength', 0.5)

        # More aggressive quality adjustment - good signals get rewarded
        quality_score = (confidence * 0.6 + signal_strength * 0.4)
        quality_multiplier = 0.7 + (quality_score * 0.6)  # Range: 0.7 to 1.3

        # Flow with market momentum - adapt to conditions
        momentum_factor = 1.0
        if 'price_momentum' in signal:
            momentum = abs(signal['price_momentum'])
            if momentum > 0.02:  # Strong momentum
                momentum_factor = 1.1  # Slightly increase position
            elif momentum < 0.005:  # Weak momentum
                momentum_factor = 0.9  # Slightly decrease position

        final_multiplier = base_multiplier * quality_multiplier * momentum_factor
        return max(0.15, min(1.0, final_multiplier))

    def _calculate_position_multiplier_enhanced(
        self,
        risk_score: float,
        signal: Dict[str, Any],
        probability_risk: Dict[str, Any]
    ) -> float:
        """Phase 2: Enhanced position multiplier calculation with probability-based adjustment"""
        try:
            # Get base multiplier from traditional calculation
            base_multiplier = self._calculate_position_multiplier(risk_score, signal)

            # Get probability-based adjustments
            trading_recommendations = probability_risk.get('trading_recommendations', {})
            recommended_multiplier = trading_recommendations.get('position_size_multiplier', 0.8)

            # Get probability factors
            factors = probability_risk.get('factors', {})
            edge_strength = factors.get('edge_strength', 'unknown')
            overall_confidence = factors.get('overall_confidence', 0.5)
            opportunity_score = factors.get('opportunity_score', 0)

            # Calculate probability-based adjustment
            probability_adjustment = 1.0

            if edge_strength == 'strong':
                probability_adjustment = 1.2  # Increase position for strong edge
            elif edge_strength == 'moderate':
                probability_adjustment = 1.1  # Slight increase for moderate edge
            elif edge_strength == 'weak':
                probability_adjustment = 0.9  # Slight decrease for weak edge
            elif edge_strength == 'negligible':
                probability_adjustment = 0.8  # Decrease for no edge

            # Adjust for confidence
            confidence_adjustment = 0.8 + (overall_confidence * 0.4)  # Range: 0.8 to 1.2

            # Adjust for opportunity quality
            opportunity_adjustment = 0.9 + (opportunity_score * 0.2)  # Range: 0.9 to 1.1

            # Combine all adjustments
            combined_adjustment = probability_adjustment * confidence_adjustment * opportunity_adjustment

            # Apply to base multiplier
            enhanced_multiplier = base_multiplier * combined_adjustment

            # Also consider the direct recommendation from probability engine
            final_multiplier = (enhanced_multiplier * 0.7) + (recommended_multiplier * 0.3)

            # Ensure reasonable bounds
            return max(0.1, min(final_multiplier, 1.5))  # Allow up to 1.5x for exceptional opportunities

        except Exception as e:
            self.logger.error(f"Enhanced position multiplier calculation failed: {e}")
            return self._calculate_position_multiplier(risk_score, signal)
    
    def _calculate_dynamic_stop_loss(
        self, 
        signal: Dict[str, Any], 
        risk_score: float, 
        market_conditions: Dict[str, Any]
    ) -> Optional[float]:
        """Calculate dynamic stop loss based on risk and market conditions"""
        
        entry_price = signal.get('entry_price', 0)
        if entry_price <= 0:
            return None
        
        # Base stop loss percentage
        base_stop_pct = self.default_stop_loss_pct
        
        # Adjust for risk score
        if risk_score > 60:
            base_stop_pct *= 0.8  # Tighter stop for high risk
        elif risk_score < 30:
            base_stop_pct *= 1.2  # Wider stop for low risk
        
        # Adjust for market volatility
        volatility = market_conditions.get('price_volatility', 0.01)
        if volatility > self.high_volatility_threshold:
            base_stop_pct *= (1 + volatility * 10)  # Wider stop in volatile markets
        
        # Cap stop loss
        final_stop_pct = min(base_stop_pct, self.max_stop_loss_pct)
        
        # Calculate stop loss price
        if signal.get('signal_direction') == 'BUY':
            stop_loss_price = entry_price * (1 - final_stop_pct)
        else:  # SELL
            stop_loss_price = entry_price * (1 + final_stop_pct)
        
        return stop_loss_price
    
    def _calculate_take_profit(self, signal: Dict[str, Any], risk_score: float) -> Optional[float]:
        """Calculate take profit target"""
        target_price = signal.get('target_price')
        if target_price:
            return target_price
        
        entry_price = signal.get('entry_price', 0)
        if entry_price <= 0:
            return None
        
        # Base take profit (risk-reward ratio of 2:1)
        base_profit_pct = self.default_stop_loss_pct * 2
        
        # Adjust for risk score (lower risk = higher target)
        if risk_score < 30:
            base_profit_pct *= 1.5
        elif risk_score > 60:
            base_profit_pct *= 0.8
        
        # Calculate take profit price
        if signal.get('signal_direction') == 'BUY':
            take_profit_price = entry_price * (1 + base_profit_pct)
        else:  # SELL
            take_profit_price = entry_price * (1 - base_profit_pct)
        
        return take_profit_price
    
    def _calculate_max_hold_time(self, signal: Dict[str, Any], risk_score: float) -> Optional[int]:
        """Calculate maximum hold time in minutes"""
        base_hold_time = signal.get('max_hold_time', 15)  # 15 minutes default
        
        # Adjust for risk score
        if risk_score > 60:
            base_hold_time = int(base_hold_time * 0.7)  # Shorter hold for high risk
        elif risk_score < 30:
            base_hold_time = int(base_hold_time * 1.3)  # Longer hold for low risk
        
        return max(5, min(base_hold_time, 30))  # 5-30 minute range
    
    def _generate_risk_warnings(self, risk_factors: List[Dict[str, Any]], risk_score: float) -> List[str]:
        """Generate risk warnings"""
        warnings = []
        
        if risk_score > 100:
            warnings.append("EXTREME RISK: Consider avoiding this trade")
        elif risk_score > 60:
            warnings.append("HIGH RISK: Use reduced position size")
        
        for factor in risk_factors:
            if factor['risk_score'] > 20:
                category = factor['category']
                warnings.append(f"Elevated {category} risk detected")
        
        return warnings

    def _generate_risk_warnings_enhanced(
        self,
        risk_factors: List[Dict[str, Any]],
        risk_score: float,
        probability_risk: Dict[str, Any]
    ) -> List[str]:
        """Phase 2: Enhanced risk warnings with probability insights"""
        try:
            # Get base warnings
            warnings = self._generate_risk_warnings(risk_factors, risk_score)

            # Add probability-based warnings
            factors = probability_risk.get('factors', {})
            edge_strength = factors.get('edge_strength', 'unknown')
            market_vs_ai_agreement = factors.get('market_vs_ai_agreement', 'unknown')
            overall_confidence = factors.get('overall_confidence', 0.5)

            # Probability-specific warnings
            if edge_strength == 'negligible':
                warnings.append("PROBABILITY WARNING: No significant edge detected - consider avoiding trade")
            elif market_vs_ai_agreement == 'disagree':
                warnings.append("PROBABILITY WARNING: Market and AI predictions disagree - higher uncertainty")

            if overall_confidence < 0.3:
                warnings.append("CONFIDENCE WARNING: Very low confidence in probability assessment")

            # Trading recommendation warnings
            trading_recommendations = probability_risk.get('trading_recommendations', {})
            trade_recommendation = trading_recommendations.get('trade_recommendation', 'HOLD')
            if trade_recommendation == 'HOLD':
                warnings.append("PROBABILITY RECOMMENDATION: Probability engine suggests HOLD")

            return warnings

        except Exception as e:
            self.logger.error(f"Enhanced warning generation failed: {e}")
            return self._generate_risk_warnings(risk_factors, risk_score)
    
    def _generate_risk_reasoning(self, risk_factors: List[Dict[str, Any]], risk_score: float) -> str:
        """Generate risk assessment reasoning"""
        high_risk_factors = [f for f in risk_factors if f['risk_score'] > 15]
        
        if not high_risk_factors:
            return "Low risk trade with favorable conditions"
        
        reasoning_parts = []
        for factor in high_risk_factors:
            category = factor['category']
            score = factor['risk_score']
            reasoning_parts.append(f"{category} risk ({score:.0f} points)")
        
        return f"Risk factors: {', '.join(reasoning_parts)}"

    def _generate_risk_reasoning_enhanced(
        self,
        risk_factors: List[Dict[str, Any]],
        risk_score: float,
        probability_risk: Dict[str, Any]
    ) -> str:
        """Phase 2: Enhanced risk reasoning with probability insights"""
        try:
            # Get base reasoning
            base_reasoning = self._generate_risk_reasoning(risk_factors, risk_score)

            # Add probability-based reasoning
            factors = probability_risk.get('factors', {})
            edge_strength = factors.get('edge_strength', 'unknown')
            overall_edge = factors.get('overall_edge', 0)
            overall_confidence = factors.get('overall_confidence', 0.5)
            opportunity_score = factors.get('opportunity_score', 0)

            probability_parts = []

            # Edge analysis
            if edge_strength != 'unknown':
                edge_direction = "positive" if overall_edge > 0 else "negative" if overall_edge < 0 else "neutral"
                probability_parts.append(f"{edge_strength} {edge_direction} edge ({overall_edge:.3f})")

            # Confidence analysis
            confidence_level = "high" if overall_confidence > 0.7 else "medium" if overall_confidence > 0.4 else "low"
            probability_parts.append(f"{confidence_level} confidence ({overall_confidence:.2f})")

            # Opportunity analysis
            if opportunity_score > 0:
                opportunity_quality = "excellent" if opportunity_score > 0.2 else "good" if opportunity_score > 0.12 else "fair"
                probability_parts.append(f"{opportunity_quality} opportunity ({opportunity_score:.3f})")

            # Trading recommendation
            trading_recommendations = probability_risk.get('trading_recommendations', {})
            trade_recommendation = trading_recommendations.get('trade_recommendation', 'HOLD')
            if trade_recommendation != 'HOLD':
                probability_parts.append(f"probability engine recommends {trade_recommendation}")

            # Combine reasoning
            if probability_parts:
                probability_reasoning = f"Probability analysis: {', '.join(probability_parts)}"
                return f"{base_reasoning}. {probability_reasoning}"
            else:
                return base_reasoning

        except Exception as e:
            self.logger.error(f"Enhanced reasoning generation failed: {e}")
            return self._generate_risk_reasoning(risk_factors, risk_score)
    
    def _conservative_risk_assessment(self) -> RiskAssessment:
        """Return conservative risk assessment when calculation fails"""
        return RiskAssessment(
            risk_level=RiskLevel.HIGH,
            risk_score=80,
            position_size_multiplier=0.3,
            recommended_stop_loss=None,
            recommended_take_profit=None,
            max_hold_time=10,
            warnings=["Risk assessment failed - using conservative settings"],
            reasoning="Unable to calculate risk - defaulting to conservative approach"
        )
