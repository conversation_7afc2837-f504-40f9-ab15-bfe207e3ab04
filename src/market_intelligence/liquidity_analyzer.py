"""
Liquidity Flow Analysis Engine
Tracks real-time liquidity changes across DEXs and identifies market microstructure patterns
"""

import asyncio
import statistics
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
import structlog
from dataclasses import dataclass
from enum import Enum

from src.data_collectors.price_aggregator import price_aggregator
from src.data_collectors.simple_asset_provider import simple_asset_provider
from src.data_collectors.dex_collectors import quickswap_collector, uniswap_v3_collector
from src.data_collectors.cache_manager import cache_manager, CacheKeys

logger = structlog.get_logger(__name__)


class LiquidityRegime(Enum):
    """Liquidity regime classifications"""
    ABUNDANT = "abundant"           # High liquidity across all sources
    NORMAL = "normal"              # Standard liquidity levels
    CONSTRAINED = "constrained"    # Limited liquidity, potential for slippage
    FRAGMENTED = "fragmented"      # Liquidity spread across many small pools
    CONCENTRATED = "concentrated"  # Liquidity concentrated in few large pools
    MIGRATING = "migrating"        # Active liquidity migration between sources


@dataclass
class LiquiditySnapshot:
    """Snapshot of liquidity state at a point in time"""
    timestamp: datetime
    token_address: str
    base_token: str
    total_liquidity_usd: float
    source_distribution: Dict[str, float]  # source -> liquidity_usd
    largest_pool_share: float  # Percentage of liquidity in largest pool
    pool_count: int
    average_pool_size: float
    liquidity_regime: LiquidityRegime
    migration_score: float  # 0-1, higher = more migration activity


@dataclass
class LiquidityMigrationPattern:
    """Detected liquidity migration pattern"""
    from_source: str
    to_source: str
    migration_amount_usd: float
    migration_percentage: float
    time_window_hours: int
    confidence_score: float  # 0-1


@dataclass
class LiquidityGap:
    """Detected liquidity gap causing potential mispricing"""
    token_address: str
    base_token: str
    gap_type: str  # 'cross_dex_arbitrage', 'pool_size_imbalance', 'temporary_drain'
    price_impact_estimate: float  # Estimated price impact percentage
    arbitrage_opportunity_usd: float
    affected_sources: List[str]
    confidence_score: float


class LiquidityAnalyzer:
    """
    Analyzes liquidity flow patterns across DEXs to identify:
    - Real-time liquidity changes and migration patterns
    - Temporarily mispriced assets due to liquidity gaps
    - Market microstructure inefficiencies
    """
    
    def __init__(self):
        self.cache_ttl = 120  # 2 minutes cache for liquidity analysis
        
        # Thresholds for liquidity analysis
        self.min_liquidity_usd = 10000  # Minimum liquidity to consider
        self.migration_threshold = 0.15  # 15% change indicates migration
        self.concentration_threshold = 0.7  # 70% in one pool = concentrated
        self.fragmentation_threshold = 0.1  # <10% per pool = fragmented
        self.arbitrage_threshold = 0.005  # 0.5% price difference = opportunity
        
        # Data sources
        self.dex_collectors = {
            'quickswap': quickswap_collector,
            'uniswap_v3': uniswap_v3_collector
        }
    
    async def analyze_liquidity_flow(self, token_address: str, base_token: str = "USDC") -> Dict[str, Any]:
        """
        Comprehensive liquidity flow analysis
        """
        cache_key = CacheKeys.market_intelligence(f"liquidity_flow_{token_address}_{base_token}")
        
        # Check cache first
        cached_analysis = await cache_manager.get(cache_key)
        if cached_analysis:
            logger.debug("liquidity_flow_cache_hit", token_address=token_address)
            return cached_analysis
        
        try:
            # Gather current liquidity snapshot
            current_snapshot = await self._create_liquidity_snapshot(token_address, base_token)
            
            # Analyze historical liquidity patterns
            migration_patterns = await self._detect_migration_patterns(token_address, base_token)
            
            # Identify liquidity gaps and arbitrage opportunities
            liquidity_gaps = await self._detect_liquidity_gaps(token_address, base_token, current_snapshot)
            
            # Calculate liquidity quality metrics
            quality_metrics = await self._calculate_liquidity_quality(current_snapshot)
            
            analysis_result = {
                'token_address': token_address,
                'base_token': base_token,
                'current_snapshot': {
                    'timestamp': current_snapshot.timestamp.isoformat(),
                    'total_liquidity_usd': current_snapshot.total_liquidity_usd,
                    'liquidity_regime': current_snapshot.liquidity_regime.value,
                    'source_distribution': current_snapshot.source_distribution,
                    'largest_pool_share': current_snapshot.largest_pool_share,
                    'pool_count': current_snapshot.pool_count,
                    'migration_score': current_snapshot.migration_score
                },
                'migration_patterns': [
                    {
                        'from_source': pattern.from_source,
                        'to_source': pattern.to_source,
                        'migration_amount_usd': pattern.migration_amount_usd,
                        'migration_percentage': pattern.migration_percentage,
                        'confidence_score': pattern.confidence_score
                    }
                    for pattern in migration_patterns
                ],
                'liquidity_gaps': [
                    {
                        'gap_type': gap.gap_type,
                        'price_impact_estimate': gap.price_impact_estimate,
                        'arbitrage_opportunity_usd': gap.arbitrage_opportunity_usd,
                        'affected_sources': gap.affected_sources,
                        'confidence_score': gap.confidence_score
                    }
                    for gap in liquidity_gaps
                ],
                'quality_metrics': quality_metrics,
                'analysis_timestamp': datetime.now().isoformat()
            }
            
            # Cache result
            await cache_manager.set(cache_key, analysis_result, self.cache_ttl)
            
            logger.info(
                "liquidity_flow_analyzed",
                token_address=token_address,
                total_liquidity=current_snapshot.total_liquidity_usd,
                regime=current_snapshot.liquidity_regime.value,
                migration_patterns=len(migration_patterns),
                liquidity_gaps=len(liquidity_gaps)
            )
            
            return analysis_result
            
        except Exception as e:
            logger.error(
                "liquidity_flow_analysis_failed",
                token_address=token_address,
                base_token=base_token,
                error=str(e)
            )
            return {
                'error': 'liquidity_flow_analysis_failed',
                'token_address': token_address,
                'base_token': base_token,
                'analysis_timestamp': datetime.now().isoformat()
            }
    
    async def _create_liquidity_snapshot(self, token_address: str, base_token: str) -> LiquiditySnapshot:
        """Create current liquidity snapshot across all sources"""
        try:
            # Get current price data which includes liquidity info
            current_price_data = await price_aggregator.get_token_price(token_address, base_token)
            
            if not current_price_data or 'sources' not in current_price_data:
                # Fallback to direct DEX queries
                return await self._create_snapshot_from_dex_data(token_address, base_token)
            
            # Extract liquidity data from price aggregator sources
            sources = current_price_data['sources']
            source_distribution = {}
            total_liquidity = 0
            
            for source in sources:
                liquidity_usd = source.get('liquidity_usd', 0)
                source_name = source.get('source', 'unknown')
                source_distribution[source_name] = liquidity_usd
                total_liquidity += liquidity_usd
            
            # Calculate distribution metrics
            pool_count = len([s for s in source_distribution.values() if s > 0])
            average_pool_size = total_liquidity / pool_count if pool_count > 0 else 0
            largest_pool_share = max(source_distribution.values()) / total_liquidity if total_liquidity > 0 else 0
            
            # Determine liquidity regime
            liquidity_regime = self._classify_liquidity_regime(
                total_liquidity, largest_pool_share, pool_count, source_distribution
            )
            
            # Calculate migration score (placeholder - would need historical data)
            migration_score = 0.0
            
            return LiquiditySnapshot(
                timestamp=datetime.now(),
                token_address=token_address,
                base_token=base_token,
                total_liquidity_usd=total_liquidity,
                source_distribution=source_distribution,
                largest_pool_share=largest_pool_share,
                pool_count=pool_count,
                average_pool_size=average_pool_size,
                liquidity_regime=liquidity_regime,
                migration_score=migration_score
            )
            
        except Exception as e:
            logger.error("liquidity_snapshot_creation_failed", error=str(e))
            # Return empty snapshot
            return LiquiditySnapshot(
                timestamp=datetime.now(),
                token_address=token_address,
                base_token=base_token,
                total_liquidity_usd=0,
                source_distribution={},
                largest_pool_share=0,
                pool_count=0,
                average_pool_size=0,
                liquidity_regime=LiquidityRegime.NORMAL,
                migration_score=0
            )
    
    async def _create_snapshot_from_dex_data(self, token_address: str, base_token: str) -> LiquiditySnapshot:
        """Create snapshot by directly querying DEX data"""
        try:
            # Query both QuickSwap and Uniswap V3
            tasks = [
                self._get_quickswap_liquidity(token_address, base_token),
                self._get_uniswap_v3_liquidity(token_address, base_token)
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            source_distribution = {}
            total_liquidity = 0
            
            # Process QuickSwap results
            if not isinstance(results[0], Exception) and results[0]:
                quickswap_liquidity = results[0]
                source_distribution['quickswap'] = quickswap_liquidity
                total_liquidity += quickswap_liquidity
            
            # Process Uniswap V3 results
            if not isinstance(results[1], Exception) and results[1]:
                uniswap_liquidity = results[1]
                source_distribution['uniswap_v3'] = uniswap_liquidity
                total_liquidity += uniswap_liquidity
            
            # Calculate metrics
            pool_count = len([s for s in source_distribution.values() if s > 0])
            average_pool_size = total_liquidity / pool_count if pool_count > 0 else 0
            largest_pool_share = max(source_distribution.values()) / total_liquidity if total_liquidity > 0 else 0
            
            liquidity_regime = self._classify_liquidity_regime(
                total_liquidity, largest_pool_share, pool_count, source_distribution
            )
            
            return LiquiditySnapshot(
                timestamp=datetime.now(),
                token_address=token_address,
                base_token=base_token,
                total_liquidity_usd=total_liquidity,
                source_distribution=source_distribution,
                largest_pool_share=largest_pool_share,
                pool_count=pool_count,
                average_pool_size=average_pool_size,
                liquidity_regime=liquidity_regime,
                migration_score=0.0
            )
            
        except Exception as e:
            logger.error("dex_snapshot_creation_failed", error=str(e))
            return LiquiditySnapshot(
                timestamp=datetime.now(),
                token_address=token_address,
                base_token=base_token,
                total_liquidity_usd=0,
                source_distribution={},
                largest_pool_share=0,
                pool_count=0,
                average_pool_size=0,
                liquidity_regime=LiquidityRegime.NORMAL,
                migration_score=0
            )


    async def _get_quickswap_liquidity(self, token_address: str, base_token: str) -> float:
        """Get liquidity from QuickSwap for this token pair"""
        try:
            top_pairs = await quickswap_collector.get_top_pairs(50)

            for pair in top_pairs:
                token0_addr = pair.get('token0', {}).get('id', '').lower()
                token1_addr = pair.get('token1', {}).get('id', '').lower()
                token0_symbol = pair.get('token0', {}).get('symbol', '')
                token1_symbol = pair.get('token1', {}).get('symbol', '')

                target_token_addr = token_address.lower()

                # Check if this pair contains our target token and base token
                if ((token0_addr == target_token_addr and token1_symbol == base_token) or
                    (token1_addr == target_token_addr and token0_symbol == base_token)):

                    return float(pair.get('reserveUSD', 0))

            return 0.0

        except Exception as e:
            logger.error("quickswap_liquidity_query_failed", error=str(e))
            return 0.0

    async def _get_uniswap_v3_liquidity(self, token_address: str, base_token: str) -> float:
        """Get liquidity from Uniswap V3 for this token pair"""
        try:
            top_pools = await uniswap_v3_collector.get_top_pools(50)

            total_liquidity = 0.0
            for pool in top_pools:
                token0_addr = pool.get('token0', {}).get('id', '').lower()
                token1_addr = pool.get('token1', {}).get('id', '').lower()
                token0_symbol = pool.get('token0', {}).get('symbol', '')
                token1_symbol = pool.get('token1', {}).get('symbol', '')

                target_token_addr = token_address.lower()

                # Check if this pool contains our target token and base token
                if ((token0_addr == target_token_addr and token1_symbol == base_token) or
                    (token1_addr == target_token_addr and token0_symbol == base_token)):

                    total_liquidity += float(pool.get('totalValueLockedUSD', 0))

            return total_liquidity

        except Exception as e:
            logger.error("uniswap_v3_liquidity_query_failed", error=str(e))
            return 0.0

    def _classify_liquidity_regime(self, total_liquidity: float, largest_pool_share: float,
                                 pool_count: int, source_distribution: Dict[str, float]) -> LiquidityRegime:
        """Classify the current liquidity regime"""
        try:
            # Check for abundant liquidity
            if total_liquidity > 10_000_000:  # $10M+
                return LiquidityRegime.ABUNDANT

            # Check for concentrated liquidity
            if largest_pool_share > self.concentration_threshold:
                return LiquidityRegime.CONCENTRATED

            # Check for fragmented liquidity
            if pool_count > 5 and all(share < self.fragmentation_threshold for share in source_distribution.values()):
                return LiquidityRegime.FRAGMENTED

            # Check for constrained liquidity
            if total_liquidity < self.min_liquidity_usd:
                return LiquidityRegime.CONSTRAINED

            # Default to normal
            return LiquidityRegime.NORMAL

        except Exception as e:
            logger.error("liquidity_regime_classification_failed", error=str(e))
            return LiquidityRegime.NORMAL

    async def _detect_migration_patterns(self, token_address: str, base_token: str) -> List[LiquidityMigrationPattern]:
        """Detect liquidity migration patterns over time"""
        try:
            # For now, return empty list - would need historical liquidity data
            # In a full implementation, this would:
            # 1. Get historical liquidity snapshots from cache/database
            # 2. Compare liquidity distribution changes over time
            # 3. Identify significant migrations between sources
            # 4. Calculate confidence scores based on data quality

            logger.debug("migration_pattern_detection_placeholder", token_address=token_address)
            return []

        except Exception as e:
            logger.error("migration_pattern_detection_failed", error=str(e))
            return []

    async def _detect_liquidity_gaps(self, token_address: str, base_token: str,
                                   snapshot: LiquiditySnapshot) -> List[LiquidityGap]:
        """Detect liquidity gaps that could cause mispricing"""
        try:
            gaps = []

            # Check for cross-DEX arbitrage opportunities
            if len(snapshot.source_distribution) >= 2:
                # Get current prices from different sources
                price_data = await price_aggregator.get_token_price(token_address, base_token)

                if price_data and 'sources' in price_data:
                    sources = price_data['sources']
                    prices = [(s.get('price', 0), s.get('source', '')) for s in sources if s.get('price', 0) > 0]

                    if len(prices) >= 2:
                        prices.sort(key=lambda x: x[0])  # Sort by price
                        min_price, min_source = prices[0]
                        max_price, max_source = prices[-1]

                        if min_price > 0:
                            price_diff_pct = (max_price - min_price) / min_price

                            if price_diff_pct > self.arbitrage_threshold:
                                # Estimate arbitrage opportunity
                                min_liquidity = snapshot.source_distribution.get(min_source, 0)
                                max_liquidity = snapshot.source_distribution.get(max_source, 0)

                                # Conservative estimate: use smaller liquidity pool
                                available_liquidity = min(min_liquidity, max_liquidity)
                                arbitrage_opportunity = available_liquidity * price_diff_pct * 0.5  # 50% efficiency

                                gaps.append(LiquidityGap(
                                    token_address=token_address,
                                    base_token=base_token,
                                    gap_type='cross_dex_arbitrage',
                                    price_impact_estimate=price_diff_pct,
                                    arbitrage_opportunity_usd=arbitrage_opportunity,
                                    affected_sources=[min_source, max_source],
                                    confidence_score=0.8 if available_liquidity > 50000 else 0.5
                                ))

            # Check for pool size imbalances
            if snapshot.largest_pool_share > 0.9 and snapshot.pool_count > 1:
                # Very concentrated liquidity could indicate temporary drain in other pools
                gaps.append(LiquidityGap(
                    token_address=token_address,
                    base_token=base_token,
                    gap_type='pool_size_imbalance',
                    price_impact_estimate=0.02,  # Estimated 2% impact
                    arbitrage_opportunity_usd=snapshot.total_liquidity_usd * 0.01,  # 1% of total
                    affected_sources=list(snapshot.source_distribution.keys()),
                    confidence_score=0.6
                ))

            return gaps

        except Exception as e:
            logger.error("liquidity_gap_detection_failed", error=str(e))
            return []

    async def _calculate_liquidity_quality(self, snapshot: LiquiditySnapshot) -> Dict[str, Any]:
        """Calculate liquidity quality metrics"""
        try:
            # Liquidity depth score (0-1, higher = better)
            depth_score = min(snapshot.total_liquidity_usd / 1_000_000, 1.0)  # Normalize to $1M

            # Distribution score (0-1, higher = more distributed)
            if snapshot.pool_count <= 1:
                distribution_score = 0.0
            else:
                # Calculate Herfindahl-Hirschman Index for concentration
                total_liquidity = snapshot.total_liquidity_usd
                if total_liquidity > 0:
                    hhi = sum((liquidity / total_liquidity) ** 2 for liquidity in snapshot.source_distribution.values())
                    distribution_score = 1.0 - hhi  # Invert so higher = more distributed
                else:
                    distribution_score = 0.0

            # Stability score (placeholder - would need historical data)
            stability_score = 0.7  # Default moderate stability

            # Overall quality score
            quality_score = (depth_score * 0.4 + distribution_score * 0.3 + stability_score * 0.3)

            return {
                'depth_score': depth_score,
                'distribution_score': distribution_score,
                'stability_score': stability_score,
                'overall_quality_score': quality_score,
                'liquidity_regime': snapshot.liquidity_regime.value,
                'total_liquidity_usd': snapshot.total_liquidity_usd,
                'pool_count': snapshot.pool_count
            }

        except Exception as e:
            logger.error("liquidity_quality_calculation_failed", error=str(e))
            return {
                'depth_score': 0.0,
                'distribution_score': 0.0,
                'stability_score': 0.0,
                'overall_quality_score': 0.0,
                'liquidity_regime': 'normal',
                'total_liquidity_usd': 0.0,
                'pool_count': 0
            }


# Global liquidity analyzer instance
liquidity_analyzer = LiquidityAnalyzer()
