"""
Volatility Intelligence Analyzer
Calculates realized volatility from real price history and compares with market expectations
"""

import asyncio
import math
import statistics
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
import structlog

from src.data_collectors.price_aggregator import price_aggregator
from src.data_collectors.simple_asset_provider import simple_asset_provider
from src.data_collectors.cache_manager import cache_manager, CacheKeys

logger = structlog.get_logger(__name__)


class VolatilityAnalyzer:
    """
    Analyzes realized volatility from real market data and compares with market expectations
    """
    
    def __init__(self):
        self.cache_ttl = 300  # 5 minutes cache for volatility calculations
        self.min_data_points = 10  # Minimum data points for reliable volatility calculation
        self.volatility_windows = {
            '1h': 1,
            '4h': 4, 
            '24h': 24
        }
        
    async def analyze_asset_volatility(self, token_address: str, base_token: str = "USDC") -> Dict[str, Any]:
        """
        Comprehensive volatility analysis for an asset
        Returns realized volatility across multiple timeframes and market expectations
        """
        cache_key = CacheKeys.price_data(f"volatility_{token_address}_{base_token}", "analysis")
        
        # Check cache first
        cached_analysis = await cache_manager.get(cache_key)
        if cached_analysis:
            logger.debug("volatility_cache_hit", token_address=token_address)
            return cached_analysis
        
        try:
            # Calculate realized volatility for different windows
            volatility_analysis = {}
            
            for window_name, hours in self.volatility_windows.items():
                volatility_data = await self._calculate_realized_volatility(
                    token_address, base_token, hours
                )
                volatility_analysis[window_name] = volatility_data
            
            # Get current market data for comparison
            current_market_data = await self._get_current_market_context(token_address, base_token)
            
            # Calculate expected daily move using the formula: realized_volatility ÷ 16
            daily_volatility = volatility_analysis.get('24h', {}).get('annualized_volatility', 0)
            expected_daily_move = daily_volatility / 16 if daily_volatility > 0 else 0
            
            # Detect volatility regime
            volatility_regime = self._detect_volatility_regime(volatility_analysis)
            
            # Compare with market expectations
            market_expectation_analysis = await self._analyze_market_expectations(
                token_address, base_token, volatility_analysis, current_market_data
            )
            
            analysis_result = {
                'token_address': token_address,
                'base_token': base_token,
                'timestamp': datetime.now().isoformat(),
                'volatility_windows': volatility_analysis,
                'expected_daily_move_pct': expected_daily_move,
                'volatility_regime': volatility_regime,
                'market_expectations': market_expectation_analysis,
                'current_market_data': current_market_data,
                'analysis_quality': self._assess_analysis_quality(volatility_analysis)
            }
            
            # Cache the result
            await cache_manager.set(cache_key, analysis_result, self.cache_ttl)
            
            logger.info(
                "volatility_analysis_completed",
                token_address=token_address,
                expected_daily_move=expected_daily_move,
                regime=volatility_regime,
                quality=analysis_result['analysis_quality']
            )
            
            return analysis_result
            
        except Exception as e:
            logger.error(
                "volatility_analysis_failed",
                token_address=token_address,
                error=str(e)
            )
            return self._get_default_volatility_analysis(token_address, base_token)
    
    async def _calculate_realized_volatility(self, token_address: str, base_token: str, hours: int) -> Dict[str, Any]:
        """Calculate realized volatility from historical price data"""
        try:
            # Get historical price data
            historical_prices = await price_aggregator.get_historical_prices(
                token_address, base_token, hours, interval="1h"
            )
            
            if len(historical_prices) < self.min_data_points:
                logger.warning(
                    "insufficient_price_data",
                    token_address=token_address,
                    hours=hours,
                    data_points=len(historical_prices)
                )
                return self._get_default_volatility_data(hours)
            
            # Calculate price returns
            returns = []
            for i in range(1, len(historical_prices)):
                prev_price = historical_prices[i-1]['price']
                curr_price = historical_prices[i]['price']
                
                if prev_price > 0 and curr_price > 0:
                    return_pct = (curr_price - prev_price) / prev_price
                    returns.append(return_pct)
            
            if len(returns) < 2:
                return self._get_default_volatility_data(hours)
            
            # Calculate volatility metrics
            mean_return = statistics.mean(returns)
            variance = statistics.variance(returns, mean_return)
            volatility = math.sqrt(variance)
            
            # Annualize volatility (assuming 24 hours = 1 day, 365 days = 1 year)
            periods_per_year = 365 * 24 / hours
            annualized_volatility = volatility * math.sqrt(periods_per_year) * 100  # Convert to percentage
            
            # Calculate additional metrics
            max_return = max(returns)
            min_return = min(returns)
            return_range = max_return - min_return
            
            # Calculate volume-weighted volatility if volume data available
            volume_weighted_volatility = self._calculate_volume_weighted_volatility(
                historical_prices, returns
            )
            
            return {
                'window_hours': hours,
                'data_points': len(returns),
                'realized_volatility': volatility,
                'annualized_volatility': annualized_volatility,
                'mean_return': mean_return,
                'max_return': max_return,
                'min_return': min_return,
                'return_range': return_range,
                'volume_weighted_volatility': volume_weighted_volatility,
                'data_quality': len(returns) / max(hours, 1),  # Data completeness ratio
                'calculation_timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(
                "realized_volatility_calculation_failed",
                token_address=token_address,
                hours=hours,
                error=str(e)
            )
            return self._get_default_volatility_data(hours)
    
    def _calculate_volume_weighted_volatility(self, price_data: List[Dict], returns: List[float]) -> float:
        """Calculate volume-weighted volatility"""
        try:
            if len(price_data) != len(returns) + 1:
                return 0.0
            
            weighted_returns = []
            total_volume = 0
            
            for i, return_val in enumerate(returns):
                volume = price_data[i+1].get('volume', 0)
                if volume > 0:
                    weighted_returns.append(return_val * volume)
                    total_volume += volume
            
            if total_volume == 0 or not weighted_returns:
                return 0.0
            
            # Calculate volume-weighted variance
            weighted_mean = sum(weighted_returns) / total_volume
            weighted_variance = sum(
                ((ret * vol / total_volume) - weighted_mean) ** 2 
                for ret, vol in zip(returns, [price_data[i+1].get('volume', 0) for i in range(len(returns))])
                if vol > 0
            )
            
            return math.sqrt(weighted_variance) if weighted_variance > 0 else 0.0
            
        except Exception as e:
            logger.warning(f"Volume-weighted volatility calculation failed: {e}")
            return 0.0
    
    async def _get_current_market_context(self, token_address: str, base_token: str) -> Dict[str, Any]:
        """Get current market context for volatility comparison"""
        try:
            # Get current price data
            current_price_data = await price_aggregator.get_token_price(token_address, base_token)
            
            if not current_price_data:
                return {}
            
            # Extract market context
            total_liquidity = sum(
                source.get('liquidity_usd', 0) 
                for source in current_price_data.get('sources', [])
            )
            
            total_volume_24h = sum(
                source.get('volume_24h_usd', 0) 
                for source in current_price_data.get('sources', [])
            )
            
            return {
                'current_price': current_price_data.get('price', 0),
                'total_liquidity_usd': total_liquidity,
                'volume_24h_usd': total_volume_24h,
                'volume_to_liquidity_ratio': total_volume_24h / total_liquidity if total_liquidity > 0 else 0,
                'price_confidence': current_price_data.get('confidence', 0),
                'sources_count': len(current_price_data.get('sources', [])),
                'timestamp': current_price_data.get('timestamp')
            }
            
        except Exception as e:
            logger.error(f"Failed to get current market context: {e}")
            return {}
    
    def _detect_volatility_regime(self, volatility_analysis: Dict[str, Any]) -> str:
        """Detect current volatility regime based on multi-timeframe analysis"""
        try:
            # Get volatility values for different windows
            vol_1h = volatility_analysis.get('1h', {}).get('annualized_volatility', 0)
            vol_4h = volatility_analysis.get('4h', {}).get('annualized_volatility', 0)
            vol_24h = volatility_analysis.get('24h', {}).get('annualized_volatility', 0)
            
            # Define volatility thresholds (in percentage)
            low_vol_threshold = 20
            medium_vol_threshold = 50
            high_vol_threshold = 100
            
            # Analyze volatility expansion/contraction
            vol_expanding = vol_1h > vol_4h > vol_24h
            vol_contracting = vol_1h < vol_4h < vol_24h
            
            # Determine regime
            avg_volatility = (vol_1h + vol_4h + vol_24h) / 3
            
            if avg_volatility < low_vol_threshold:
                regime = "low_volatility"
            elif avg_volatility < medium_vol_threshold:
                regime = "normal_volatility"
            elif avg_volatility < high_vol_threshold:
                regime = "elevated_volatility"
            else:
                regime = "high_volatility"
            
            # Add expansion/contraction context
            if vol_expanding:
                regime += "_expanding"
            elif vol_contracting:
                regime += "_contracting"
            else:
                regime += "_stable"
            
            return regime
            
        except Exception as e:
            logger.warning(f"Volatility regime detection failed: {e}")
            return "unknown_regime"
    
    async def _analyze_market_expectations(self, token_address: str, base_token: str, 
                                         volatility_analysis: Dict[str, Any], 
                                         market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze market expectations vs realized volatility"""
        try:
            # Get volume patterns to infer market expectations
            volume_24h = market_data.get('volume_24h_usd', 0)
            liquidity = market_data.get('total_liquidity_usd', 1)
            
            # High volume relative to liquidity suggests market expects movement
            volume_liquidity_ratio = volume_24h / liquidity if liquidity > 0 else 0
            
            # Analyze if current volatility is higher/lower than what volume suggests
            realized_vol_24h = volatility_analysis.get('24h', {}).get('annualized_volatility', 0)
            
            # Simple heuristic: high volume/liquidity ratio suggests market expects volatility
            expected_volatility_from_volume = min(volume_liquidity_ratio * 100, 200)  # Cap at 200%
            
            volatility_surprise = realized_vol_24h - expected_volatility_from_volume
            
            # Determine market efficiency
            if abs(volatility_surprise) < 10:  # Within 10% is considered efficient
                efficiency_assessment = "efficient"
            elif volatility_surprise > 10:
                efficiency_assessment = "underpriced_volatility"  # Market didn't expect this much movement
            else:
                efficiency_assessment = "overpriced_volatility"  # Market expected more movement
            
            return {
                'volume_liquidity_ratio': volume_liquidity_ratio,
                'expected_volatility_from_volume': expected_volatility_from_volume,
                'realized_volatility_24h': realized_vol_24h,
                'volatility_surprise': volatility_surprise,
                'efficiency_assessment': efficiency_assessment,
                'market_expects_movement': volume_liquidity_ratio > 0.5,  # Threshold for "expecting movement"
                'analysis_timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Market expectations analysis failed: {e}")
            return {
                'efficiency_assessment': 'unknown',
                'market_expects_movement': False,
                'analysis_timestamp': datetime.now().isoformat()
            }
    
    def _assess_analysis_quality(self, volatility_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Assess the quality of volatility analysis"""
        try:
            quality_scores = []
            data_points_total = 0
            
            for window, data in volatility_analysis.items():
                if isinstance(data, dict):
                    data_quality = data.get('data_quality', 0)
                    data_points = data.get('data_points', 0)
                    
                    quality_scores.append(data_quality)
                    data_points_total += data_points
            
            overall_quality = statistics.mean(quality_scores) if quality_scores else 0
            
            # Determine quality level
            if overall_quality >= 0.8:
                quality_level = "high"
            elif overall_quality >= 0.5:
                quality_level = "medium"
            else:
                quality_level = "low"
            
            return {
                'overall_quality_score': overall_quality,
                'quality_level': quality_level,
                'total_data_points': data_points_total,
                'windows_analyzed': len(quality_scores),
                'reliable_for_trading': overall_quality >= 0.6
            }
            
        except Exception as e:
            logger.warning(f"Quality assessment failed: {e}")
            return {
                'overall_quality_score': 0,
                'quality_level': 'unknown',
                'reliable_for_trading': False
            }
    
    def _get_default_volatility_data(self, hours: int) -> Dict[str, Any]:
        """Return default volatility data when calculation fails"""
        return {
            'window_hours': hours,
            'data_points': 0,
            'realized_volatility': 0,
            'annualized_volatility': 0,
            'mean_return': 0,
            'max_return': 0,
            'min_return': 0,
            'return_range': 0,
            'volume_weighted_volatility': 0,
            'data_quality': 0,
            'calculation_timestamp': datetime.now().isoformat()
        }
    
    def _get_default_volatility_analysis(self, token_address: str, base_token: str) -> Dict[str, Any]:
        """Return default analysis when full analysis fails"""
        return {
            'token_address': token_address,
            'base_token': base_token,
            'timestamp': datetime.now().isoformat(),
            'volatility_windows': {
                '1h': self._get_default_volatility_data(1),
                '4h': self._get_default_volatility_data(4),
                '24h': self._get_default_volatility_data(24)
            },
            'expected_daily_move_pct': 0,
            'volatility_regime': 'unknown_regime',
            'market_expectations': {
                'efficiency_assessment': 'unknown',
                'market_expects_movement': False
            },
            'current_market_data': {},
            'analysis_quality': {
                'overall_quality_score': 0,
                'quality_level': 'low',
                'reliable_for_trading': False
            }
        }


# Global volatility analyzer instance
volatility_analyzer = VolatilityAnalyzer()
