"""
Market Inefficiency Detection Engine
Detects market inefficiencies using real DEX liquidity data and Coinglass sentiment data
"""

import asyncio
import statistics
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
import structlog

from src.data_collectors.price_aggregator import price_aggregator
from src.data_collectors.simple_asset_provider import simple_asset_provider
from src.data_collectors.coinglass_client import CoinglassClient
from src.data_collectors.cache_manager import cache_manager, CacheKeys
from .volatility_analyzer import volatility_analyzer

logger = structlog.get_logger(__name__)


class InefficiencyDetector:
    """
    Detects market inefficiencies through analysis of:
    - Supply/demand imbalances from DEX liquidity data
    - Abnormal volume-to-liquidity ratios
    - Sentiment-driven trading patterns from Coinglass data
    - High media coverage events (unusual social sentiment spikes)
    """
    
    def __init__(self):
        self.coinglass_client = CoinglassClient()
        self.cache_ttl = 180  # 3 minutes cache for inefficiency detection
        
        # Thresholds for inefficiency detection
        self.volume_liquidity_anomaly_threshold = 3.0  # 3x normal ratio
        self.sentiment_spike_threshold = 0.3  # 30% change in sentiment
        self.liquidity_imbalance_threshold = 0.7  # 70% liquidity on one side
        self.liquidation_anomaly_threshold = 2.0  # 2x normal liquidations
        
    async def detect_market_inefficiencies(self, token_address: str, symbol: str, base_token: str = "USDC") -> Dict[str, Any]:
        """
        Comprehensive market inefficiency detection for an asset
        """
        cache_key = CacheKeys.price_data(f"inefficiency_{token_address}_{symbol}", "detection")
        
        # Check cache first
        cached_detection = await cache_manager.get(cache_key)
        if cached_detection:
            logger.debug("inefficiency_cache_hit", token_address=token_address, symbol=symbol)
            return cached_detection
        
        try:
            # Run all inefficiency detection methods concurrently
            detection_tasks = [
                self._detect_supply_demand_imbalances(token_address, base_token),
                self._detect_volume_liquidity_anomalies(token_address, base_token),
                self._detect_sentiment_driven_patterns(symbol),
                self._detect_liquidation_anomalies(symbol),
                self._detect_cross_exchange_arbitrage_opportunities(token_address, base_token)
            ]
            
            results = await asyncio.gather(*detection_tasks, return_exceptions=True)
            
            # Process results
            supply_demand_analysis = results[0] if not isinstance(results[0], Exception) else {}
            volume_liquidity_analysis = results[1] if not isinstance(results[1], Exception) else {}
            sentiment_analysis = results[2] if not isinstance(results[2], Exception) else {}
            liquidation_analysis = results[3] if not isinstance(results[3], Exception) else {}
            arbitrage_analysis = results[4] if not isinstance(results[4], Exception) else {}
            
            # Calculate overall inefficiency score
            inefficiency_score = self._calculate_inefficiency_score({
                'supply_demand': supply_demand_analysis,
                'volume_liquidity': volume_liquidity_analysis,
                'sentiment': sentiment_analysis,
                'liquidation': liquidation_analysis,
                'arbitrage': arbitrage_analysis
            })
            
            # Identify specific opportunities
            opportunities = self._identify_trading_opportunities({
                'supply_demand': supply_demand_analysis,
                'volume_liquidity': volume_liquidity_analysis,
                'sentiment': sentiment_analysis,
                'liquidation': liquidation_analysis,
                'arbitrage': arbitrage_analysis
            })
            
            detection_result = {
                'token_address': token_address,
                'symbol': symbol,
                'base_token': base_token,
                'timestamp': datetime.now().isoformat(),
                'inefficiency_score': inefficiency_score,
                'supply_demand_analysis': supply_demand_analysis,
                'volume_liquidity_analysis': volume_liquidity_analysis,
                'sentiment_analysis': sentiment_analysis,
                'liquidation_analysis': liquidation_analysis,
                'arbitrage_analysis': arbitrage_analysis,
                'trading_opportunities': opportunities,
                'detection_quality': self._assess_detection_quality(results)
            }
            
            # Cache the result
            await cache_manager.set(cache_key, detection_result, self.cache_ttl)
            
            logger.info(
                "inefficiency_detection_completed",
                token_address=token_address,
                symbol=symbol,
                inefficiency_score=inefficiency_score,
                opportunities_count=len(opportunities)
            )
            
            return detection_result
            
        except Exception as e:
            logger.error(
                "inefficiency_detection_failed",
                token_address=token_address,
                symbol=symbol,
                error=str(e)
            )
            return self._get_default_inefficiency_analysis(token_address, symbol, base_token)
    
    async def _detect_supply_demand_imbalances(self, token_address: str, base_token: str) -> Dict[str, Any]:
        """Detect supply/demand imbalances from DEX liquidity data"""
        try:
            # Get current market data
            current_price_data = await price_aggregator.get_token_price(token_address, base_token)
            
            if not current_price_data or not current_price_data.get('sources'):
                return {'imbalance_detected': False, 'reason': 'no_market_data'}
            
            # Analyze liquidity distribution across sources
            sources = current_price_data['sources']
            total_liquidity = sum(source.get('liquidity_usd', 0) for source in sources)
            
            if total_liquidity == 0:
                return {'imbalance_detected': False, 'reason': 'no_liquidity_data'}
            
            # Calculate liquidity concentration
            liquidity_distribution = []
            for source in sources:
                liquidity_pct = source.get('liquidity_usd', 0) / total_liquidity
                liquidity_distribution.append({
                    'source': source.get('source', 'unknown'),
                    'liquidity_usd': source.get('liquidity_usd', 0),
                    'liquidity_percentage': liquidity_pct,
                    'price': source.get('price', 0)
                })
            
            # Sort by liquidity
            liquidity_distribution.sort(key=lambda x: x['liquidity_percentage'], reverse=True)
            
            # Check for concentration (top source has >70% of liquidity)
            top_source_concentration = liquidity_distribution[0]['liquidity_percentage']
            is_concentrated = top_source_concentration > self.liquidity_imbalance_threshold
            
            # Check for price discrepancies between sources
            prices = [source.get('price', 0) for source in sources if source.get('price', 0) > 0]
            price_spread = 0
            if len(prices) > 1:
                price_spread = (max(prices) - min(prices)) / min(prices)
            
            return {
                'imbalance_detected': is_concentrated or price_spread > 0.02,  # 2% price spread threshold
                'total_liquidity_usd': total_liquidity,
                'top_source_concentration': top_source_concentration,
                'price_spread_percentage': price_spread,
                'liquidity_distribution': liquidity_distribution,
                'sources_count': len(sources),
                'analysis_timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Supply/demand imbalance detection failed: {e}")
            return {'imbalance_detected': False, 'error': str(e)}
    
    async def _detect_volume_liquidity_anomalies(self, token_address: str, base_token: str) -> Dict[str, Any]:
        """Detect abnormal volume-to-liquidity ratios"""
        try:
            # Get current market data
            current_price_data = await price_aggregator.get_token_price(token_address, base_token)
            
            if not current_price_data:
                return {'anomaly_detected': False, 'reason': 'no_market_data'}
            
            # Calculate total volume and liquidity
            sources = current_price_data.get('sources', [])
            total_liquidity = sum(source.get('liquidity_usd', 0) for source in sources)
            total_volume_24h = sum(source.get('volume_24h_usd', 0) for source in sources)
            
            if total_liquidity == 0:
                return {'anomaly_detected': False, 'reason': 'no_liquidity_data'}
            
            # Calculate volume-to-liquidity ratio
            volume_liquidity_ratio = total_volume_24h / total_liquidity
            
            # Get historical context by checking similar assets
            historical_context = await self._get_volume_liquidity_historical_context(token_address)
            
            # Detect anomaly
            is_anomaly = volume_liquidity_ratio > self.volume_liquidity_anomaly_threshold
            
            # Additional analysis: volume distribution across sources
            volume_distribution = []
            for source in sources:
                volume_24h = source.get('volume_24h_usd', 0)
                volume_pct = volume_24h / total_volume_24h if total_volume_24h > 0 else 0
                volume_distribution.append({
                    'source': source.get('source', 'unknown'),
                    'volume_24h_usd': volume_24h,
                    'volume_percentage': volume_pct
                })
            
            return {
                'anomaly_detected': is_anomaly,
                'volume_liquidity_ratio': volume_liquidity_ratio,
                'total_volume_24h_usd': total_volume_24h,
                'total_liquidity_usd': total_liquidity,
                'anomaly_threshold': self.volume_liquidity_anomaly_threshold,
                'volume_distribution': volume_distribution,
                'historical_context': historical_context,
                'analysis_timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Volume-liquidity anomaly detection failed: {e}")
            return {'anomaly_detected': False, 'error': str(e)}
    
    async def _detect_sentiment_driven_patterns(self, symbol: str) -> Dict[str, Any]:
        """Detect sentiment-driven trading patterns using Coinglass data"""
        try:
            # Get sentiment data from Coinglass
            sentiment_tasks = [
                self.coinglass_client.get_liquidation_data(symbol, '24h'),
                self.coinglass_client.get_funding_rates(symbol),
                self.coinglass_client.get_long_short_ratio(symbol),
                self.coinglass_client.get_fear_greed_index()
            ]
            
            results = await asyncio.gather(*sentiment_tasks, return_exceptions=True)
            
            liquidation_data = results[0] if not isinstance(results[0], Exception) else {}
            funding_data = results[1] if not isinstance(results[1], Exception) else {}
            long_short_data = results[2] if not isinstance(results[2], Exception) else {}
            fear_greed_data = results[3] if not isinstance(results[3], Exception) else {}
            
            # Analyze sentiment patterns
            sentiment_analysis = {
                'liquidation_pressure': self._analyze_liquidation_pressure(liquidation_data),
                'funding_bias': self._analyze_funding_bias(funding_data),
                'position_imbalance': self._analyze_position_imbalance(long_short_data),
                'market_fear_greed': self._analyze_fear_greed(fear_greed_data)
            }
            
            # Detect sentiment-driven anomalies
            sentiment_spike_detected = any([
                sentiment_analysis['liquidation_pressure'].get('anomaly', False),
                sentiment_analysis['funding_bias'].get('extreme', False),
                sentiment_analysis['position_imbalance'].get('imbalanced', False)
            ])
            
            return {
                'sentiment_spike_detected': sentiment_spike_detected,
                'liquidation_analysis': sentiment_analysis['liquidation_pressure'],
                'funding_analysis': sentiment_analysis['funding_bias'],
                'position_analysis': sentiment_analysis['position_imbalance'],
                'fear_greed_analysis': sentiment_analysis['market_fear_greed'],
                'overall_sentiment_score': self._calculate_sentiment_score(sentiment_analysis),
                'analysis_timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Sentiment pattern detection failed: {e}")
            return {'sentiment_spike_detected': False, 'error': str(e)}
    
    async def _detect_liquidation_anomalies(self, symbol: str) -> Dict[str, Any]:
        """Detect liquidation anomalies that might indicate market inefficiencies"""
        try:
            # Get liquidation data for different timeframes
            liquidation_tasks = [
                self.coinglass_client.get_liquidation_data(symbol, '1h'),
                self.coinglass_client.get_liquidation_data(symbol, '4h'),
                self.coinglass_client.get_liquidation_data(symbol, '24h')
            ]
            
            results = await asyncio.gather(*liquidation_tasks, return_exceptions=True)
            
            liq_1h = results[0] if not isinstance(results[0], Exception) else {}
            liq_4h = results[1] if not isinstance(results[1], Exception) else {}
            liq_24h = results[2] if not isinstance(results[2], Exception) else {}
            
            # Analyze liquidation patterns
            liquidation_1h = liq_1h.get('totalLiquidation', 0)
            liquidation_4h = liq_4h.get('totalLiquidation', 0)
            liquidation_24h = liq_24h.get('totalLiquidation', 0)
            
            # Calculate liquidation velocity (recent vs historical)
            liquidation_velocity = liquidation_1h / (liquidation_24h / 24) if liquidation_24h > 0 else 0
            
            # Detect anomalies
            anomaly_detected = liquidation_velocity > self.liquidation_anomaly_threshold
            
            # Analyze long vs short liquidations
            long_liq_24h = liq_24h.get('longLiquidation', 0)
            short_liq_24h = liq_24h.get('shortLiquidation', 0)
            total_liq_24h = long_liq_24h + short_liq_24h
            
            liquidation_bias = 'neutral'
            if total_liq_24h > 0:
                long_ratio = long_liq_24h / total_liq_24h
                if long_ratio > 0.7:
                    liquidation_bias = 'long_heavy'
                elif long_ratio < 0.3:
                    liquidation_bias = 'short_heavy'
            
            return {
                'anomaly_detected': anomaly_detected,
                'liquidation_velocity': liquidation_velocity,
                'liquidation_1h': liquidation_1h,
                'liquidation_4h': liquidation_4h,
                'liquidation_24h': liquidation_24h,
                'long_liquidation_24h': long_liq_24h,
                'short_liquidation_24h': short_liq_24h,
                'liquidation_bias': liquidation_bias,
                'velocity_threshold': self.liquidation_anomaly_threshold,
                'analysis_timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Liquidation anomaly detection failed: {e}")
            return {'anomaly_detected': False, 'error': str(e)}
    
    async def _detect_cross_exchange_arbitrage_opportunities(self, token_address: str, base_token: str) -> Dict[str, Any]:
        """Detect arbitrage opportunities between different DEX sources"""
        try:
            # Get current price data from multiple sources
            current_price_data = await price_aggregator.get_token_price(token_address, base_token)
            
            if not current_price_data or len(current_price_data.get('sources', [])) < 2:
                return {'arbitrage_opportunity': False, 'reason': 'insufficient_sources'}
            
            sources = current_price_data['sources']
            
            # Find price discrepancies
            prices_with_liquidity = [
                {
                    'source': source.get('source', 'unknown'),
                    'price': source.get('price', 0),
                    'liquidity_usd': source.get('liquidity_usd', 0)
                }
                for source in sources 
                if source.get('price', 0) > 0 and source.get('liquidity_usd', 0) > 1000  # Min $1000 liquidity
            ]
            
            if len(prices_with_liquidity) < 2:
                return {'arbitrage_opportunity': False, 'reason': 'insufficient_liquid_sources'}
            
            # Sort by price
            prices_with_liquidity.sort(key=lambda x: x['price'])
            
            lowest_price_source = prices_with_liquidity[0]
            highest_price_source = prices_with_liquidity[-1]
            
            # Calculate arbitrage opportunity
            price_difference = highest_price_source['price'] - lowest_price_source['price']
            arbitrage_percentage = price_difference / lowest_price_source['price']
            
            # Consider transaction costs (rough estimate: 0.3% per trade * 2 trades = 0.6%)
            estimated_transaction_cost = 0.006
            net_arbitrage_opportunity = arbitrage_percentage - estimated_transaction_cost
            
            arbitrage_viable = net_arbitrage_opportunity > 0.002  # 0.2% minimum profit threshold
            
            return {
                'arbitrage_opportunity': arbitrage_viable,
                'gross_arbitrage_percentage': arbitrage_percentage,
                'net_arbitrage_percentage': net_arbitrage_opportunity,
                'buy_source': lowest_price_source,
                'sell_source': highest_price_source,
                'estimated_transaction_cost': estimated_transaction_cost,
                'min_profit_threshold': 0.002,
                'sources_analyzed': len(prices_with_liquidity),
                'analysis_timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Arbitrage opportunity detection failed: {e}")
            return {'arbitrage_opportunity': False, 'error': str(e)}
    
    async def _get_volume_liquidity_historical_context(self, token_address: str) -> Dict[str, Any]:
        """Get historical context for volume-liquidity ratios"""
        try:
            # Get top assets to compare ratios
            top_assets = await simple_asset_provider.get_top_pairs(20)
            
            if not top_assets:
                return {'context_available': False}
            
            # Calculate volume-liquidity ratios for comparison
            ratios = []
            for asset in top_assets:
                liquidity = asset.get('liquidity_usd', 0)
                volume = asset.get('volume_24h_usd', 0)
                if liquidity > 0:
                    ratio = volume / liquidity
                    ratios.append(ratio)
            
            if not ratios:
                return {'context_available': False}
            
            # Calculate statistics
            median_ratio = statistics.median(ratios)
            mean_ratio = statistics.mean(ratios)
            std_ratio = statistics.stdev(ratios) if len(ratios) > 1 else 0
            
            return {
                'context_available': True,
                'market_median_ratio': median_ratio,
                'market_mean_ratio': mean_ratio,
                'market_std_ratio': std_ratio,
                'sample_size': len(ratios),
                'analysis_timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.warning(f"Historical context analysis failed: {e}")
            return {'context_available': False, 'error': str(e)}
    
    def _analyze_liquidation_pressure(self, liquidation_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze liquidation pressure patterns"""
        total_liq = liquidation_data.get('totalLiquidation', 0)
        long_liq = liquidation_data.get('longLiquidation', 0)
        short_liq = liquidation_data.get('shortLiquidation', 0)
        
        # Detect anomalies (simplified heuristic)
        anomaly = total_liq > 1000000  # $1M+ liquidations is considered high
        
        return {
            'total_liquidation': total_liq,
            'long_liquidation': long_liq,
            'short_liquidation': short_liq,
            'anomaly': anomaly,
            'pressure_level': 'high' if anomaly else 'normal'
        }
    
    def _analyze_funding_bias(self, funding_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze funding rate bias"""
        if not funding_data:
            return {'extreme': False, 'bias': 'neutral'}
        
        # This would need to be implemented based on actual Coinglass API response structure
        return {'extreme': False, 'bias': 'neutral', 'note': 'funding_analysis_placeholder'}
    
    def _analyze_position_imbalance(self, long_short_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze long/short position imbalance"""
        if not long_short_data:
            return {'imbalanced': False, 'bias': 'neutral'}
        
        # This would need to be implemented based on actual Coinglass API response structure
        return {'imbalanced': False, 'bias': 'neutral', 'note': 'position_analysis_placeholder'}
    
    def _analyze_fear_greed(self, fear_greed_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze fear and greed index"""
        if not fear_greed_data:
            return {'extreme_sentiment': False, 'level': 'neutral'}
        
        # This would need to be implemented based on actual Coinglass API response structure
        return {'extreme_sentiment': False, 'level': 'neutral', 'note': 'fear_greed_placeholder'}
    
    def _calculate_sentiment_score(self, sentiment_analysis: Dict[str, Any]) -> float:
        """Calculate overall sentiment score"""
        # Simplified scoring - would be enhanced with real data
        return 0.5  # Neutral score placeholder
    
    def _calculate_inefficiency_score(self, analyses: Dict[str, Any]) -> float:
        """Calculate overall inefficiency score from all analyses"""
        try:
            scores = []
            
            # Supply/demand imbalance score
            if analyses['supply_demand'].get('imbalance_detected'):
                scores.append(0.8)
            
            # Volume/liquidity anomaly score
            if analyses['volume_liquidity'].get('anomaly_detected'):
                scores.append(0.7)
            
            # Sentiment spike score
            if analyses['sentiment'].get('sentiment_spike_detected'):
                scores.append(0.6)
            
            # Liquidation anomaly score
            if analyses['liquidation'].get('anomaly_detected'):
                scores.append(0.5)
            
            # Arbitrage opportunity score
            if analyses['arbitrage'].get('arbitrage_opportunity'):
                scores.append(0.9)
            
            # Calculate weighted average
            if scores:
                return min(sum(scores) / len(scores), 1.0)
            else:
                return 0.0
                
        except Exception as e:
            logger.warning(f"Inefficiency score calculation failed: {e}")
            return 0.0
    
    def _identify_trading_opportunities(self, analyses: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Identify specific trading opportunities from inefficiency analyses"""
        opportunities = []
        
        try:
            # Arbitrage opportunities
            if analyses['arbitrage'].get('arbitrage_opportunity'):
                opportunities.append({
                    'type': 'arbitrage',
                    'description': 'Cross-exchange price discrepancy detected',
                    'confidence': 0.8,
                    'expected_profit': analyses['arbitrage'].get('net_arbitrage_percentage', 0),
                    'details': analyses['arbitrage']
                })
            
            # Supply/demand imbalance opportunities
            if analyses['supply_demand'].get('imbalance_detected'):
                opportunities.append({
                    'type': 'liquidity_imbalance',
                    'description': 'Liquidity concentration detected',
                    'confidence': 0.6,
                    'details': analyses['supply_demand']
                })
            
            # Volume anomaly opportunities
            if analyses['volume_liquidity'].get('anomaly_detected'):
                opportunities.append({
                    'type': 'volume_anomaly',
                    'description': 'Unusual volume-to-liquidity ratio',
                    'confidence': 0.5,
                    'details': analyses['volume_liquidity']
                })
            
            return opportunities
            
        except Exception as e:
            logger.warning(f"Opportunity identification failed: {e}")
            return []
    
    def _assess_detection_quality(self, results: List[Any]) -> Dict[str, Any]:
        """Assess the quality of inefficiency detection"""
        successful_analyses = sum(1 for result in results if not isinstance(result, Exception))
        total_analyses = len(results)
        
        quality_score = successful_analyses / total_analyses if total_analyses > 0 else 0
        
        return {
            'successful_analyses': successful_analyses,
            'total_analyses': total_analyses,
            'quality_score': quality_score,
            'reliable_for_trading': quality_score >= 0.6
        }
    
    def _get_default_inefficiency_analysis(self, token_address: str, symbol: str, base_token: str) -> Dict[str, Any]:
        """Return default analysis when detection fails"""
        return {
            'token_address': token_address,
            'symbol': symbol,
            'base_token': base_token,
            'timestamp': datetime.now().isoformat(),
            'inefficiency_score': 0,
            'supply_demand_analysis': {'imbalance_detected': False},
            'volume_liquidity_analysis': {'anomaly_detected': False},
            'sentiment_analysis': {'sentiment_spike_detected': False},
            'liquidation_analysis': {'anomaly_detected': False},
            'arbitrage_analysis': {'arbitrage_opportunity': False},
            'trading_opportunities': [],
            'detection_quality': {'quality_score': 0, 'reliable_for_trading': False}
        }


# Global inefficiency detector instance
inefficiency_detector = InefficiencyDetector()
