"""
Probability Assessment Engine
Advanced probability-based risk assessment using real market data
Reframes risk as "market pricing X% chance, we think Y%"
"""

import asyncio
import logging
import math
import statistics
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from ..data_collectors.price_aggregator import price_aggregator
from ..data_collectors.cache_manager import cache_manager, CacheKeys
from .volatility_analyzer import volatility_analyzer

logger = logging.getLogger(__name__)


class ProbabilityType(Enum):
    """Types of probability assessments"""
    DIRECTIONAL_MOVE = "directional_move"  # Probability of price moving in predicted direction
    MAGNITUDE_MOVE = "magnitude_move"      # Probability of achieving target magnitude
    VOLATILITY_EXPANSION = "volatility_expansion"  # Probability of volatility increasing
    REGIME_CHANGE = "regime_change"        # Probability of volatility regime changing


@dataclass
class ProbabilityAssessment:
    """Probability assessment result"""
    probability_type: ProbabilityType
    market_implied_probability: float  # What market is pricing (0-1)
    ai_predicted_probability: float    # What our AI thinks (0-1)
    probability_edge: float            # AI - Market (positive = we think higher chance)
    confidence_level: float            # How confident we are in our assessment (0-1)
    supporting_evidence: Dict[str, Any]
    assessment_timestamp: str
    

class ProbabilityEngine:
    """
    Advanced probability assessment engine for trading decisions
    Uses real market data to calibrate probability models and compare with market expectations
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Probability calibration parameters
        self.historical_lookback_days = 30
        self.min_historical_samples = 50
        self.confidence_decay_factor = 0.95  # Confidence decreases with time
        
        # Market probability inference parameters
        self.volatility_probability_scaling = 2.0  # How volatility maps to probability
        self.volume_probability_weight = 0.3       # Weight of volume in probability inference
        
        # Cache settings
        self.cache_ttl = 300  # 5 minutes
        
    async def assess_directional_probability(
        self, 
        token_address: str, 
        predicted_direction: str,  # 'BUY' or 'SELL'
        predicted_magnitude: float,  # Expected price change percentage
        timeframe_hours: int = 24,
        base_token: str = "USDC"
    ) -> ProbabilityAssessment:
        """
        Assess probability of directional price movement
        Compares AI prediction with market-implied probabilities
        """
        cache_key = CacheKeys.price_data(
            f"prob_directional_{token_address}_{predicted_direction}_{timeframe_hours}", 
            "assessment"
        )
        
        # Check cache
        cached_assessment = await cache_manager.get(cache_key)
        if cached_assessment:
            return ProbabilityAssessment(**cached_assessment)
        
        try:
            # Get historical data for calibration
            historical_accuracy = await self._get_historical_directional_accuracy(
                token_address, base_token, timeframe_hours
            )
            
            # Get current market conditions
            market_conditions = await self._get_current_market_conditions(token_address, base_token)
            
            # Calculate market-implied probability from volatility and volume
            market_implied_prob = await self._calculate_market_implied_directional_probability(
                market_conditions, predicted_magnitude, timeframe_hours
            )
            
            # Calculate AI predicted probability based on historical performance
            ai_predicted_prob = await self._calculate_ai_directional_probability(
                historical_accuracy, market_conditions, predicted_magnitude
            )
            
            # Calculate probability edge
            probability_edge = ai_predicted_prob - market_implied_prob
            
            # Assess confidence based on data quality and historical performance
            confidence_level = self._calculate_confidence_level(
                historical_accuracy, market_conditions
            )
            
            # Gather supporting evidence
            supporting_evidence = {
                'historical_accuracy': historical_accuracy,
                'market_conditions': market_conditions,
                'predicted_magnitude': predicted_magnitude,
                'timeframe_hours': timeframe_hours,
                'volatility_regime': market_conditions.get('volatility_regime', 'unknown')
            }
            
            assessment = ProbabilityAssessment(
                probability_type=ProbabilityType.DIRECTIONAL_MOVE,
                market_implied_probability=market_implied_prob,
                ai_predicted_probability=ai_predicted_prob,
                probability_edge=probability_edge,
                confidence_level=confidence_level,
                supporting_evidence=supporting_evidence,
                assessment_timestamp=datetime.now().isoformat()
            )
            
            # Cache the result
            await cache_manager.set(cache_key, assessment.__dict__, self.cache_ttl)
            
            self.logger.info(
                f"directional_probability_assessed: token={token_address}, direction={predicted_direction}, "
                f"market_prob={market_implied_prob:.3f}, ai_prob={ai_predicted_prob:.3f}, "
                f"edge={probability_edge:.3f}, confidence={confidence_level:.2f}"
            )
            
            return assessment
            
        except Exception as e:
            self.logger.error(f"directional_probability_assessment_failed: token={token_address}, error={str(e)}")
            return self._get_default_probability_assessment(ProbabilityType.DIRECTIONAL_MOVE)
    
    async def assess_volatility_expansion_probability(
        self, 
        token_address: str,
        current_volatility: float,
        expected_volatility: float,
        timeframe_hours: int = 24,
        base_token: str = "USDC"
    ) -> ProbabilityAssessment:
        """
        Assess probability of volatility expansion
        Uses volatility regime analysis from Phase 1
        """
        try:
            # Get volatility analysis from Phase 1
            volatility_analysis = await volatility_analyzer.analyze_asset_volatility(
                token_address, base_token
            )
            
            # Calculate market-implied volatility expansion probability
            market_implied_prob = await self._calculate_market_implied_volatility_probability(
                volatility_analysis, expected_volatility, timeframe_hours
            )
            
            # Calculate AI predicted probability based on regime analysis
            ai_predicted_prob = await self._calculate_ai_volatility_probability(
                volatility_analysis, current_volatility, expected_volatility
            )
            
            # Calculate probability edge
            probability_edge = ai_predicted_prob - market_implied_prob
            
            # Assess confidence based on volatility analysis quality
            confidence_level = volatility_analysis.get('analysis_quality', {}).get('overall_quality_score', 0.5)
            
            supporting_evidence = {
                'volatility_analysis': volatility_analysis,
                'current_volatility': current_volatility,
                'expected_volatility': expected_volatility,
                'volatility_regime': volatility_analysis.get('volatility_regime', 'unknown'),
                'market_expectations': volatility_analysis.get('market_expectations', {})
            }
            
            assessment = ProbabilityAssessment(
                probability_type=ProbabilityType.VOLATILITY_EXPANSION,
                market_implied_probability=market_implied_prob,
                ai_predicted_probability=ai_predicted_prob,
                probability_edge=probability_edge,
                confidence_level=confidence_level,
                supporting_evidence=supporting_evidence,
                assessment_timestamp=datetime.now().isoformat()
            )
            
            self.logger.info(
                f"volatility_expansion_probability_assessed: token={token_address}, "
                f"current_vol={current_volatility:.3f}, expected_vol={expected_volatility:.3f}, "
                f"market_prob={market_implied_prob:.3f}, ai_prob={ai_predicted_prob:.3f}, edge={probability_edge:.3f}"
            )
            
            return assessment
            
        except Exception as e:
            self.logger.error(f"volatility_expansion_probability_assessment_failed: token={token_address}, error={str(e)}")
            return self._get_default_probability_assessment(ProbabilityType.VOLATILITY_EXPANSION)
    
    async def _get_historical_directional_accuracy(
        self, 
        token_address: str, 
        base_token: str, 
        timeframe_hours: int
    ) -> Dict[str, Any]:
        """Get historical accuracy data for directional predictions"""
        try:
            # Get historical price data
            end_time = datetime.now()
            start_time = end_time - timedelta(days=self.historical_lookback_days)
            
            historical_prices = await price_aggregator.get_historical_prices(
                token_address, base_token, self.historical_lookback_days * 24, interval="1h"
            )
            
            if len(historical_prices) < self.min_historical_samples:
                return {'accuracy_rate': 0.5, 'sample_count': 0, 'data_quality': 'insufficient'}
            
            # Analyze historical directional accuracy
            correct_predictions = 0
            total_predictions = 0
            
            for i in range(timeframe_hours, len(historical_prices) - timeframe_hours):
                current_price = historical_prices[i]['price']
                future_price = historical_prices[i + timeframe_hours]['price']
                
                if current_price > 0 and future_price > 0:
                    actual_direction = 'BUY' if future_price > current_price else 'SELL'
                    # For now, assume 50% accuracy as baseline - this would be enhanced with actual ML model performance
                    predicted_direction = 'BUY' if (i % 2 == 0) else 'SELL'  # Placeholder logic
                    
                    if actual_direction == predicted_direction:
                        correct_predictions += 1
                    total_predictions += 1
            
            accuracy_rate = correct_predictions / total_predictions if total_predictions > 0 else 0.5
            
            return {
                'accuracy_rate': accuracy_rate,
                'sample_count': total_predictions,
                'data_quality': 'sufficient' if total_predictions >= self.min_historical_samples else 'limited',
                'timeframe_hours': timeframe_hours
            }
            
        except Exception as e:
            self.logger.error(f"Historical accuracy calculation failed: {e}")
            return {'accuracy_rate': 0.5, 'sample_count': 0, 'data_quality': 'error'}
    
    async def _get_current_market_conditions(self, token_address: str, base_token: str) -> Dict[str, Any]:
        """Get current market conditions for probability assessment"""
        try:
            # Get current price data
            current_price_data = await price_aggregator.get_token_price(token_address, base_token)
            
            # Get volatility analysis
            volatility_analysis = await volatility_analyzer.analyze_asset_volatility(token_address, base_token)
            
            if not current_price_data:
                return {}
            
            # Extract market conditions
            total_liquidity = sum(
                source.get('liquidity_usd', 0) 
                for source in current_price_data.get('sources', [])
            )
            
            total_volume_24h = sum(
                source.get('volume_24h_usd', 0) 
                for source in current_price_data.get('sources', [])
            )
            
            return {
                'current_price': current_price_data.get('price', 0),
                'total_liquidity_usd': total_liquidity,
                'volume_24h_usd': total_volume_24h,
                'volume_to_liquidity_ratio': total_volume_24h / total_liquidity if total_liquidity > 0 else 0,
                'volatility_regime': volatility_analysis.get('volatility_regime', 'unknown'),
                'expected_daily_move': volatility_analysis.get('expected_daily_move_pct', 0),
                'market_efficiency': volatility_analysis.get('market_expectations', {}).get('efficiency_assessment', 'unknown'),
                'timestamp': current_price_data.get('timestamp')
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get current market conditions: {e}")
            return {}

    async def _calculate_market_implied_directional_probability(
        self,
        market_conditions: Dict[str, Any],
        predicted_magnitude: float,
        timeframe_hours: int
    ) -> float:
        """Calculate market-implied probability from volatility and volume patterns"""
        try:
            # Get expected daily move from volatility analysis
            expected_daily_move = market_conditions.get('expected_daily_move', 0.02)  # Default 2%
            volume_liquidity_ratio = market_conditions.get('volume_to_liquidity_ratio', 0.1)

            # Scale expected move to timeframe
            timeframe_scaling = math.sqrt(timeframe_hours / 24)
            expected_move_timeframe = expected_daily_move * timeframe_scaling

            # Calculate probability based on predicted magnitude vs expected move
            if expected_move_timeframe > 0:
                magnitude_ratio = abs(predicted_magnitude) / expected_move_timeframe

                # Use normal distribution approximation
                # Higher magnitude relative to expected = lower probability
                base_probability = 0.5 * math.exp(-0.5 * (magnitude_ratio - 1) ** 2)

                # Adjust for volume activity (high volume = higher probability of movement)
                volume_adjustment = min(volume_liquidity_ratio * self.volume_probability_weight, 0.2)

                market_implied_prob = min(base_probability + volume_adjustment, 0.95)
            else:
                market_implied_prob = 0.5  # Default to 50% if no volatility data

            return max(0.05, market_implied_prob)  # Minimum 5% probability

        except Exception as e:
            self.logger.error(f"Market implied probability calculation failed: {e}")
            return 0.5

    async def _calculate_ai_directional_probability(
        self,
        historical_accuracy: Dict[str, Any],
        market_conditions: Dict[str, Any],
        predicted_magnitude: float
    ) -> float:
        """Calculate AI predicted probability based on historical performance and conditions"""
        try:
            base_accuracy = historical_accuracy.get('accuracy_rate', 0.5)
            sample_count = historical_accuracy.get('sample_count', 0)

            # Adjust for sample size confidence
            if sample_count < self.min_historical_samples:
                confidence_penalty = 0.1
            else:
                confidence_penalty = 0

            # Adjust for market regime
            volatility_regime = market_conditions.get('volatility_regime', 'unknown')
            regime_adjustment = 0

            if 'expanding' in volatility_regime:
                regime_adjustment = 0.05  # Slightly higher confidence in trending markets
            elif 'contracting' in volatility_regime:
                regime_adjustment = -0.05  # Lower confidence in consolidating markets

            # Adjust for magnitude confidence
            expected_daily_move = market_conditions.get('expected_daily_move', 0.02)
            if expected_daily_move > 0:
                magnitude_confidence = min(abs(predicted_magnitude) / expected_daily_move, 2.0) * 0.05
            else:
                magnitude_confidence = 0

            ai_probability = base_accuracy + regime_adjustment + magnitude_confidence - confidence_penalty

            return max(0.05, min(ai_probability, 0.95))  # Clamp between 5% and 95%

        except Exception as e:
            self.logger.error(f"AI probability calculation failed: {e}")
            return 0.5

    async def _calculate_market_implied_volatility_probability(
        self,
        volatility_analysis: Dict[str, Any],
        expected_volatility: float,
        timeframe_hours: int
    ) -> float:
        """Calculate market-implied probability of volatility expansion"""
        try:
            market_expectations = volatility_analysis.get('market_expectations', {})
            efficiency_assessment = market_expectations.get('efficiency_assessment', 'efficient')

            current_vol_24h = volatility_analysis.get('volatility_windows', {}).get('24h', {}).get('annualized_volatility', 0)

            if current_vol_24h > 0:
                volatility_ratio = expected_volatility / current_vol_24h

                # Market implied probability based on efficiency assessment
                if efficiency_assessment == 'underpriced_volatility':
                    base_prob = 0.7  # Market didn't expect current volatility, likely to continue
                elif efficiency_assessment == 'overpriced_volatility':
                    base_prob = 0.3  # Market expected more volatility than realized
                else:
                    base_prob = 0.5  # Efficient market

                # Adjust for volatility ratio
                if volatility_ratio > 1.2:  # Expecting significant expansion
                    base_prob *= 0.8
                elif volatility_ratio < 0.8:  # Expecting contraction
                    base_prob *= 1.2

                return max(0.05, min(base_prob, 0.95))
            else:
                return 0.5

        except Exception as e:
            self.logger.error(f"Market implied volatility probability calculation failed: {e}")
            return 0.5

    async def _calculate_ai_volatility_probability(
        self,
        volatility_analysis: Dict[str, Any],
        current_volatility: float,
        expected_volatility: float
    ) -> float:
        """Calculate AI predicted probability of volatility expansion"""
        try:
            volatility_regime = volatility_analysis.get('volatility_regime', 'unknown')
            analysis_quality = volatility_analysis.get('analysis_quality', {}).get('overall_quality_score', 0.5)

            # Base probability from regime analysis
            if 'expanding' in volatility_regime:
                base_prob = 0.7
            elif 'contracting' in volatility_regime:
                base_prob = 0.3
            else:
                base_prob = 0.5

            # Adjust for expected vs current volatility
            if current_volatility > 0:
                volatility_ratio = expected_volatility / current_volatility

                if volatility_ratio > 1.5:  # Expecting large expansion
                    base_prob *= 0.7  # Lower probability for extreme moves
                elif volatility_ratio > 1.1:  # Moderate expansion
                    base_prob *= 0.9
                elif volatility_ratio < 0.8:  # Expecting contraction
                    base_prob = 1 - base_prob  # Flip probability for contraction

            # Weight by analysis quality
            quality_weighted_prob = base_prob * analysis_quality + 0.5 * (1 - analysis_quality)

            return max(0.05, min(quality_weighted_prob, 0.95))

        except Exception as e:
            self.logger.error(f"AI volatility probability calculation failed: {e}")
            return 0.5

    def _calculate_confidence_level(
        self,
        historical_accuracy: Dict[str, Any],
        market_conditions: Dict[str, Any]
    ) -> float:
        """Calculate confidence level in probability assessment"""
        try:
            # Base confidence from historical accuracy
            accuracy_rate = historical_accuracy.get('accuracy_rate', 0.5)
            sample_count = historical_accuracy.get('sample_count', 0)

            # Confidence increases with accuracy and sample size
            accuracy_confidence = (accuracy_rate - 0.5) * 2  # Scale 0.5-1.0 to 0-1.0
            sample_confidence = min(sample_count / self.min_historical_samples, 1.0)

            # Market condition confidence
            market_efficiency = market_conditions.get('market_efficiency', 'unknown')
            if market_efficiency in ['underpriced_volatility', 'overpriced_volatility']:
                market_confidence = 0.8  # Higher confidence when market is inefficient
            elif market_efficiency == 'efficient':
                market_confidence = 0.6  # Moderate confidence in efficient markets
            else:
                market_confidence = 0.4  # Lower confidence when unknown

            # Combined confidence
            overall_confidence = (accuracy_confidence * 0.4 + sample_confidence * 0.3 + market_confidence * 0.3)

            return max(0.1, min(overall_confidence, 0.9))  # Clamp between 10% and 90%

        except Exception as e:
            self.logger.error(f"Confidence calculation failed: {e}")
            return 0.5

    async def calibrate_probability_models(
        self,
        token_address: str,
        base_token: str = "USDC",
        calibration_days: int = 60
    ) -> Dict[str, Any]:
        """
        Calibrate probability models using historical data
        Returns calibration metrics and model parameters
        """
        cache_key = CacheKeys.price_data(f"prob_calibration_{token_address}_{calibration_days}", "calibration")

        # Check cache
        cached_calibration = await cache_manager.get(cache_key)
        if cached_calibration:
            return cached_calibration

        try:
            # Get extended historical data for calibration
            historical_prices = await price_aggregator.get_historical_prices(
                token_address, base_token, calibration_days * 24, interval="1h"
            )

            if len(historical_prices) < calibration_days:
                return self._get_default_calibration_result()

            # Calibrate directional prediction model
            directional_calibration = await self._calibrate_directional_model(historical_prices)

            # Calibrate volatility prediction model
            volatility_calibration = await self._calibrate_volatility_model(
                token_address, base_token, historical_prices
            )

            # Calculate overall model reliability
            overall_reliability = self._calculate_model_reliability(
                directional_calibration, volatility_calibration
            )

            calibration_result = {
                'token_address': token_address,
                'base_token': base_token,
                'calibration_period_days': calibration_days,
                'directional_model': directional_calibration,
                'volatility_model': volatility_calibration,
                'overall_reliability': overall_reliability,
                'calibration_timestamp': datetime.now().isoformat(),
                'sample_count': len(historical_prices)
            }

            # Cache the calibration result (longer TTL for calibration data)
            await cache_manager.set(cache_key, calibration_result, self.cache_ttl * 12)  # 1 hour cache

            self.logger.info(
                f"probability_models_calibrated: token={token_address}, "
                f"directional_accuracy={directional_calibration.get('accuracy_rate', 0):.2f}, "
                f"volatility_accuracy={volatility_calibration.get('accuracy_rate', 0):.2f}, "
                f"overall_reliability={overall_reliability:.2f}"
            )

            return calibration_result

        except Exception as e:
            self.logger.error(f"probability_calibration_failed: token={token_address}, error={str(e)}")
            return self._get_default_calibration_result()

    async def _calibrate_directional_model(self, historical_prices: List[Dict]) -> Dict[str, Any]:
        """Calibrate directional prediction model using historical data"""
        try:
            timeframes = [1, 4, 24]  # Hours
            calibration_results = {}

            for timeframe in timeframes:
                correct_predictions = 0
                total_predictions = 0
                probability_errors = []

                # Walk through historical data
                for i in range(timeframe, len(historical_prices) - timeframe):
                    current_price = historical_prices[i]['price']
                    future_price = historical_prices[i + timeframe]['price']

                    if current_price > 0 and future_price > 0:
                        actual_return = (future_price - current_price) / current_price
                        actual_direction = 1 if actual_return > 0 else -1

                        # Simulate prediction based on recent volatility
                        recent_volatility = self._calculate_recent_volatility(
                            historical_prices[max(0, i-24):i]
                        )

                        # Simple prediction model (would be replaced with actual ML model)
                        predicted_probability = 0.5 + (recent_volatility * 0.1)  # Placeholder
                        predicted_direction = 1 if predicted_probability > 0.5 else -1

                        # Check accuracy
                        if actual_direction == predicted_direction:
                            correct_predictions += 1

                        # Calculate probability calibration error
                        actual_probability = 1.0 if actual_direction == predicted_direction else 0.0
                        probability_error = abs(predicted_probability - actual_probability)
                        probability_errors.append(probability_error)

                        total_predictions += 1

                accuracy_rate = correct_predictions / total_predictions if total_predictions > 0 else 0.5
                avg_probability_error = statistics.mean(probability_errors) if probability_errors else 0.5

                calibration_results[f'{timeframe}h'] = {
                    'accuracy_rate': accuracy_rate,
                    'total_predictions': total_predictions,
                    'avg_probability_error': avg_probability_error,
                    'calibration_quality': 1 - avg_probability_error  # Higher is better
                }

            # Calculate overall directional model performance
            overall_accuracy = statistics.mean([
                result['accuracy_rate'] for result in calibration_results.values()
            ])

            overall_calibration_quality = statistics.mean([
                result['calibration_quality'] for result in calibration_results.values()
            ])

            return {
                'timeframe_results': calibration_results,
                'overall_accuracy': overall_accuracy,
                'overall_calibration_quality': overall_calibration_quality,
                'model_type': 'directional_prediction'
            }

        except Exception as e:
            self.logger.error(f"Directional model calibration failed: {e}")
            return {'overall_accuracy': 0.5, 'overall_calibration_quality': 0.5}

    async def _calibrate_volatility_model(
        self,
        token_address: str,
        base_token: str,
        historical_prices: List[Dict]
    ) -> Dict[str, Any]:
        """Calibrate volatility prediction model using historical data"""
        try:
            volatility_predictions = []
            volatility_actuals = []

            # Analyze volatility prediction accuracy over time
            for i in range(48, len(historical_prices) - 24, 24):  # Daily intervals
                # Calculate realized volatility for past 24h
                past_24h_prices = historical_prices[i-24:i]
                past_volatility = self._calculate_recent_volatility(past_24h_prices)

                # Calculate actual volatility for next 24h
                future_24h_prices = historical_prices[i:i+24]
                future_volatility = self._calculate_recent_volatility(future_24h_prices)

                if past_volatility > 0 and future_volatility > 0:
                    # Simple volatility prediction model (would be enhanced with actual model)
                    predicted_volatility = past_volatility * 0.9  # Mean reversion assumption

                    volatility_predictions.append(predicted_volatility)
                    volatility_actuals.append(future_volatility)

            if len(volatility_predictions) < 10:
                return {'overall_accuracy': 0.5, 'prediction_error': 0.5}

            # Calculate prediction accuracy metrics
            prediction_errors = [
                abs(pred - actual) / actual
                for pred, actual in zip(volatility_predictions, volatility_actuals)
                if actual > 0
            ]

            avg_prediction_error = statistics.mean(prediction_errors) if prediction_errors else 0.5
            volatility_accuracy = max(0, 1 - avg_prediction_error)  # Convert error to accuracy

            # Calculate correlation between predictions and actuals
            correlation = self._calculate_correlation(volatility_predictions, volatility_actuals)

            return {
                'overall_accuracy': volatility_accuracy,
                'prediction_error': avg_prediction_error,
                'prediction_correlation': correlation,
                'sample_count': len(volatility_predictions),
                'model_type': 'volatility_prediction'
            }

        except Exception as e:
            self.logger.error(f"Volatility model calibration failed: {e}")
            return {'overall_accuracy': 0.5, 'prediction_error': 0.5}

    def _calculate_recent_volatility(self, price_data: List[Dict]) -> float:
        """Calculate volatility from recent price data"""
        try:
            if len(price_data) < 2:
                return 0.0

            returns = []
            for i in range(1, len(price_data)):
                prev_price = price_data[i-1]['price']
                curr_price = price_data[i]['price']

                if prev_price > 0 and curr_price > 0:
                    return_pct = (curr_price - prev_price) / prev_price
                    returns.append(return_pct)

            if len(returns) < 2:
                return 0.0

            return statistics.stdev(returns) if len(returns) > 1 else 0.0

        except Exception:
            return 0.0

    def _calculate_correlation(self, x: List[float], y: List[float]) -> float:
        """Calculate correlation coefficient between two series"""
        try:
            if len(x) != len(y) or len(x) < 2:
                return 0.0

            mean_x = statistics.mean(x)
            mean_y = statistics.mean(y)

            numerator = sum((xi - mean_x) * (yi - mean_y) for xi, yi in zip(x, y))

            sum_sq_x = sum((xi - mean_x) ** 2 for xi in x)
            sum_sq_y = sum((yi - mean_y) ** 2 for yi in y)

            denominator = math.sqrt(sum_sq_x * sum_sq_y)

            return numerator / denominator if denominator > 0 else 0.0

        except Exception:
            return 0.0

    def _calculate_model_reliability(
        self,
        directional_calibration: Dict[str, Any],
        volatility_calibration: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Calculate overall model reliability metrics"""
        try:
            directional_accuracy = directional_calibration.get('overall_accuracy', 0.5)
            directional_quality = directional_calibration.get('overall_calibration_quality', 0.5)

            volatility_accuracy = volatility_calibration.get('overall_accuracy', 0.5)
            volatility_correlation = volatility_calibration.get('prediction_correlation', 0.0)

            # Combined reliability score
            overall_reliability = (
                directional_accuracy * 0.3 +
                directional_quality * 0.2 +
                volatility_accuracy * 0.3 +
                abs(volatility_correlation) * 0.2
            )

            # Reliability classification
            if overall_reliability >= 0.7:
                reliability_class = "high"
            elif overall_reliability >= 0.5:
                reliability_class = "medium"
            else:
                reliability_class = "low"

            return {
                'overall_score': overall_reliability,
                'reliability_class': reliability_class,
                'directional_component': directional_accuracy,
                'volatility_component': volatility_accuracy,
                'recommended_confidence_multiplier': min(overall_reliability * 1.5, 1.0)
            }

        except Exception as e:
            self.logger.error(f"Model reliability calculation failed: {e}")
            return {
                'overall_score': 0.5,
                'reliability_class': 'medium',
                'recommended_confidence_multiplier': 0.75
            }

    def _get_default_calibration_result(self) -> Dict[str, Any]:
        """Return default calibration result when calibration fails"""
        return {
            'directional_model': {'overall_accuracy': 0.5, 'overall_calibration_quality': 0.5},
            'volatility_model': {'overall_accuracy': 0.5, 'prediction_error': 0.5},
            'overall_reliability': {
                'overall_score': 0.5,
                'reliability_class': 'medium',
                'recommended_confidence_multiplier': 0.75
            },
            'calibration_timestamp': datetime.now().isoformat(),
            'sample_count': 0
        }

    async def compare_market_vs_ai_probabilities(
        self,
        token_address: str,
        signal_data: Dict[str, Any],
        base_token: str = "USDC"
    ) -> Dict[str, Any]:
        """
        Comprehensive comparison of market-implied vs AI-predicted probabilities
        Returns detailed analysis of probability edges and trading opportunities
        """
        try:
            # Extract signal information
            predicted_direction = signal_data.get('signal_direction', 'BUY')
            predicted_magnitude = abs(signal_data.get('predicted_change', 0.02))
            confidence = signal_data.get('confidence', 0.5)
            timeframe_hours = signal_data.get('timeframe_hours', 24)

            # Get directional probability assessment
            directional_assessment = await self.assess_directional_probability(
                token_address, predicted_direction, predicted_magnitude, timeframe_hours, base_token
            )

            # Get volatility expansion probability if applicable
            volatility_assessment = None
            if 'expected_volatility' in signal_data:
                current_vol = signal_data.get('current_volatility', 0.02)
                expected_vol = signal_data.get('expected_volatility', 0.03)

                volatility_assessment = await self.assess_volatility_expansion_probability(
                    token_address, current_vol, expected_vol, timeframe_hours, base_token
                )

            # Calculate combined probability edge
            combined_edge = self._calculate_combined_probability_edge(
                directional_assessment, volatility_assessment, signal_data
            )

            # Assess market efficiency and opportunity quality
            opportunity_analysis = await self._analyze_trading_opportunity(
                token_address, base_token, directional_assessment, volatility_assessment, combined_edge
            )

            # Generate trading recommendations
            trading_recommendations = self._generate_probability_based_recommendations(
                directional_assessment, volatility_assessment, combined_edge, opportunity_analysis
            )

            comparison_result = {
                'token_address': token_address,
                'signal_data': signal_data,
                'directional_assessment': directional_assessment.__dict__,
                'volatility_assessment': volatility_assessment.__dict__ if volatility_assessment else None,
                'combined_probability_edge': combined_edge,
                'opportunity_analysis': opportunity_analysis,
                'trading_recommendations': trading_recommendations,
                'comparison_timestamp': datetime.now().isoformat()
            }

            self.logger.info(
                f"market_vs_ai_probability_comparison_completed: token={token_address}, "
                f"directional_edge={directional_assessment.probability_edge:.3f}, "
                f"combined_edge={combined_edge.get('overall_edge', 0):.3f}, "
                f"opportunity_score={opportunity_analysis.get('opportunity_score', 0):.3f}"
            )

            return comparison_result

        except Exception as e:
            self.logger.error(f"market_vs_ai_probability_comparison_failed: token={token_address}, error={str(e)}")
            return self._get_default_comparison_result(token_address, signal_data)

    def _calculate_combined_probability_edge(
        self,
        directional_assessment: ProbabilityAssessment,
        volatility_assessment: Optional[ProbabilityAssessment],
        signal_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Calculate combined probability edge from multiple assessments"""
        try:
            directional_edge = directional_assessment.probability_edge
            directional_confidence = directional_assessment.confidence_level

            # Weight directional edge by confidence
            weighted_directional_edge = directional_edge * directional_confidence

            # Add volatility edge if available
            volatility_edge = 0
            volatility_confidence = 0
            if volatility_assessment:
                volatility_edge = volatility_assessment.probability_edge
                volatility_confidence = volatility_assessment.confidence_level

            weighted_volatility_edge = volatility_edge * volatility_confidence

            # Combine edges with appropriate weighting
            if volatility_assessment:
                # Both directional and volatility assessments available
                overall_edge = (weighted_directional_edge * 0.7 + weighted_volatility_edge * 0.3)
                overall_confidence = (directional_confidence * 0.7 + volatility_confidence * 0.3)
            else:
                # Only directional assessment available
                overall_edge = weighted_directional_edge
                overall_confidence = directional_confidence

            # Calculate edge significance
            edge_significance = abs(overall_edge) * overall_confidence

            # Classify edge strength
            if edge_significance >= 0.15:
                edge_strength = "strong"
            elif edge_significance >= 0.08:
                edge_strength = "moderate"
            elif edge_significance >= 0.03:
                edge_strength = "weak"
            else:
                edge_strength = "negligible"

            return {
                'overall_edge': overall_edge,
                'overall_confidence': overall_confidence,
                'edge_significance': edge_significance,
                'edge_strength': edge_strength,
                'directional_component': {
                    'edge': directional_edge,
                    'confidence': directional_confidence,
                    'weighted_contribution': weighted_directional_edge
                },
                'volatility_component': {
                    'edge': volatility_edge,
                    'confidence': volatility_confidence,
                    'weighted_contribution': weighted_volatility_edge
                } if volatility_assessment else None
            }

        except Exception as e:
            self.logger.error(f"Combined probability edge calculation failed: {e}")
            return {
                'overall_edge': 0.0,
                'overall_confidence': 0.5,
                'edge_significance': 0.0,
                'edge_strength': 'negligible'
            }

    async def _analyze_trading_opportunity(
        self,
        token_address: str,
        base_token: str,
        directional_assessment: ProbabilityAssessment,
        volatility_assessment: Optional[ProbabilityAssessment],
        combined_edge: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Analyze the quality of the trading opportunity"""
        try:
            # Get current market conditions
            market_conditions = await self._get_current_market_conditions(token_address, base_token)

            # Assess market liquidity and execution feasibility
            liquidity_score = self._assess_liquidity_for_opportunity(market_conditions)

            # Assess market timing
            timing_score = self._assess_market_timing(market_conditions)

            # Calculate risk-adjusted opportunity score
            base_opportunity_score = combined_edge.get('edge_significance', 0)

            # Adjust for market conditions
            liquidity_adjustment = (liquidity_score - 0.5) * 0.2  # ±10% adjustment
            timing_adjustment = (timing_score - 0.5) * 0.1       # ±5% adjustment

            adjusted_opportunity_score = base_opportunity_score + liquidity_adjustment + timing_adjustment
            adjusted_opportunity_score = max(0, min(adjusted_opportunity_score, 1.0))

            # Classify opportunity quality
            if adjusted_opportunity_score >= 0.2:
                opportunity_quality = "excellent"
            elif adjusted_opportunity_score >= 0.12:
                opportunity_quality = "good"
            elif adjusted_opportunity_score >= 0.06:
                opportunity_quality = "fair"
            else:
                opportunity_quality = "poor"

            # Identify key risk factors
            risk_factors = self._identify_opportunity_risks(
                directional_assessment, volatility_assessment, market_conditions
            )

            return {
                'opportunity_score': adjusted_opportunity_score,
                'opportunity_quality': opportunity_quality,
                'liquidity_score': liquidity_score,
                'timing_score': timing_score,
                'market_conditions': market_conditions,
                'risk_factors': risk_factors,
                'execution_feasibility': liquidity_score > 0.6,  # Minimum liquidity threshold
                'recommended_position_size_multiplier': self._calculate_opportunity_position_multiplier(
                    adjusted_opportunity_score, combined_edge.get('overall_confidence', 0.5)
                )
            }

        except Exception as e:
            self.logger.error(f"Trading opportunity analysis failed: {e}")
            return {
                'opportunity_score': 0.0,
                'opportunity_quality': 'poor',
                'execution_feasibility': False,
                'recommended_position_size_multiplier': 0.5
            }

    def _assess_liquidity_for_opportunity(self, market_conditions: Dict[str, Any]) -> float:
        """Assess market liquidity for trade execution"""
        try:
            total_liquidity = market_conditions.get('total_liquidity_usd', 0)
            volume_24h = market_conditions.get('volume_24h_usd', 0)
            volume_liquidity_ratio = market_conditions.get('volume_to_liquidity_ratio', 0)

            # Liquidity thresholds
            min_liquidity = 100000  # $100k minimum
            good_liquidity = 1000000  # $1M for good liquidity

            # Base liquidity score
            if total_liquidity >= good_liquidity:
                liquidity_base = 0.8
            elif total_liquidity >= min_liquidity:
                liquidity_base = 0.4 + (total_liquidity - min_liquidity) / (good_liquidity - min_liquidity) * 0.4
            else:
                liquidity_base = total_liquidity / min_liquidity * 0.4

            # Adjust for volume activity
            if volume_liquidity_ratio > 1.0:  # High activity
                volume_adjustment = 0.2
            elif volume_liquidity_ratio > 0.5:  # Moderate activity
                volume_adjustment = 0.1
            else:  # Low activity
                volume_adjustment = -0.1

            liquidity_score = liquidity_base + volume_adjustment
            return max(0.0, min(liquidity_score, 1.0))

        except Exception:
            return 0.5

    def _assess_market_timing(self, market_conditions: Dict[str, Any]) -> float:
        """Assess market timing for the opportunity"""
        try:
            volatility_regime = market_conditions.get('volatility_regime', 'unknown')
            market_efficiency = market_conditions.get('market_efficiency', 'unknown')

            # Base timing score
            timing_score = 0.5

            # Adjust for volatility regime
            if 'expanding' in volatility_regime:
                timing_score += 0.2  # Good timing for trend following
            elif 'contracting' in volatility_regime:
                timing_score -= 0.1  # Challenging for momentum strategies

            # Adjust for market efficiency
            if market_efficiency in ['underpriced_volatility', 'overpriced_volatility']:
                timing_score += 0.2  # Good timing when market is inefficient
            elif market_efficiency == 'efficient':
                timing_score -= 0.1  # Harder to find edges in efficient markets

            return max(0.0, min(timing_score, 1.0))

        except Exception:
            return 0.5

    def _identify_opportunity_risks(
        self,
        directional_assessment: ProbabilityAssessment,
        volatility_assessment: Optional[ProbabilityAssessment],
        market_conditions: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Identify key risk factors for the trading opportunity"""
        risk_factors = []

        try:
            # Low confidence risk
            if directional_assessment.confidence_level < 0.4:
                risk_factors.append({
                    'risk_type': 'low_confidence',
                    'severity': 'high',
                    'description': f'Low confidence in directional assessment ({directional_assessment.confidence_level:.2f})',
                    'mitigation': 'Reduce position size or wait for higher confidence signals'
                })

            # Market efficiency risk
            market_efficiency = market_conditions.get('market_efficiency', 'unknown')
            if market_efficiency == 'efficient':
                risk_factors.append({
                    'risk_type': 'market_efficiency',
                    'severity': 'medium',
                    'description': 'Market appears efficiently priced, edges may be limited',
                    'mitigation': 'Focus on execution quality and risk management'
                })

            # Liquidity risk
            total_liquidity = market_conditions.get('total_liquidity_usd', 0)
            if total_liquidity < 500000:  # Less than $500k liquidity
                risk_factors.append({
                    'risk_type': 'liquidity_risk',
                    'severity': 'high',
                    'description': f'Low liquidity (${total_liquidity:,.0f}) may impact execution',
                    'mitigation': 'Use smaller position sizes and limit orders'
                })

            # Volatility regime risk
            volatility_regime = market_conditions.get('volatility_regime', 'unknown')
            if 'high_volatility' in volatility_regime:
                risk_factors.append({
                    'risk_type': 'high_volatility',
                    'severity': 'medium',
                    'description': 'High volatility regime increases execution risk',
                    'mitigation': 'Widen stop losses and reduce leverage'
                })

            # Conflicting signals risk
            if volatility_assessment and abs(directional_assessment.probability_edge - volatility_assessment.probability_edge) > 0.2:
                risk_factors.append({
                    'risk_type': 'conflicting_signals',
                    'severity': 'medium',
                    'description': 'Directional and volatility assessments show conflicting edges',
                    'mitigation': 'Wait for signal alignment or reduce position size'
                })

            return risk_factors

        except Exception as e:
            self.logger.error(f"Risk factor identification failed: {e}")
            return [{'risk_type': 'analysis_error', 'severity': 'high', 'description': 'Risk analysis failed'}]

    def _calculate_opportunity_position_multiplier(self, opportunity_score: float, confidence: float) -> float:
        """Calculate position size multiplier based on opportunity quality"""
        try:
            # Base multiplier from opportunity score
            base_multiplier = min(opportunity_score * 2.5, 1.5)  # Max 1.5x for excellent opportunities

            # Adjust for confidence
            confidence_adjustment = (confidence - 0.5) * 0.5  # ±25% adjustment

            final_multiplier = base_multiplier + confidence_adjustment

            # Clamp between reasonable bounds
            return max(0.3, min(final_multiplier, 1.5))

        except Exception:
            return 0.8  # Conservative default

    def _generate_probability_based_recommendations(
        self,
        directional_assessment: ProbabilityAssessment,
        volatility_assessment: Optional[ProbabilityAssessment],
        combined_edge: Dict[str, Any],
        opportunity_analysis: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate trading recommendations based on probability analysis"""
        try:
            recommendations = {
                'trade_recommendation': 'HOLD',  # Default
                'confidence_level': 'LOW',
                'position_size_multiplier': 0.8,
                'risk_management': {},
                'execution_guidance': {},
                'reasoning': []
            }

            edge_strength = combined_edge.get('edge_strength', 'negligible')
            opportunity_quality = opportunity_analysis.get('opportunity_quality', 'poor')
            overall_edge = combined_edge.get('overall_edge', 0)

            # Trade recommendation logic
            if edge_strength in ['strong', 'moderate'] and opportunity_quality in ['excellent', 'good']:
                if overall_edge > 0.05:
                    recommendations['trade_recommendation'] = 'BUY'
                elif overall_edge < -0.05:
                    recommendations['trade_recommendation'] = 'SELL'
                else:
                    recommendations['trade_recommendation'] = 'HOLD'
            else:
                recommendations['trade_recommendation'] = 'HOLD'

            # Confidence level
            overall_confidence = combined_edge.get('overall_confidence', 0.5)
            if overall_confidence >= 0.7:
                recommendations['confidence_level'] = 'HIGH'
            elif overall_confidence >= 0.5:
                recommendations['confidence_level'] = 'MEDIUM'
            else:
                recommendations['confidence_level'] = 'LOW'

            # Position sizing
            recommendations['position_size_multiplier'] = opportunity_analysis.get(
                'recommended_position_size_multiplier', 0.8
            )

            # Risk management recommendations
            recommendations['risk_management'] = {
                'stop_loss_multiplier': 1.2 if edge_strength == 'weak' else 1.0,
                'take_profit_multiplier': 1.5 if edge_strength == 'strong' else 1.0,
                'max_hold_time_hours': 48 if 'expanding' in str(directional_assessment.supporting_evidence.get('volatility_regime', '')) else 24
            }

            # Execution guidance
            recommendations['execution_guidance'] = {
                'order_type': 'LIMIT' if opportunity_analysis.get('liquidity_score', 0) < 0.7 else 'MARKET',
                'execution_urgency': 'HIGH' if edge_strength == 'strong' else 'MEDIUM',
                'slippage_tolerance': 0.5 if opportunity_analysis.get('liquidity_score', 0) > 0.8 else 1.0
            }

            # Reasoning
            reasoning = []
            reasoning.append(f"Probability edge: {overall_edge:.3f} ({edge_strength})")
            reasoning.append(f"Opportunity quality: {opportunity_quality}")
            reasoning.append(f"Market efficiency: {directional_assessment.supporting_evidence.get('market_conditions', {}).get('market_efficiency', 'unknown')}")

            if volatility_assessment:
                reasoning.append(f"Volatility edge: {volatility_assessment.probability_edge:.3f}")

            recommendations['reasoning'] = reasoning

            return recommendations

        except Exception as e:
            self.logger.error(f"Recommendation generation failed: {e}")
            return {
                'trade_recommendation': 'HOLD',
                'confidence_level': 'LOW',
                'position_size_multiplier': 0.5,
                'reasoning': ['Analysis failed - using conservative defaults']
            }

    def _get_default_comparison_result(self, token_address: str, signal_data: Dict[str, Any]) -> Dict[str, Any]:
        """Return default comparison result when analysis fails"""
        return {
            'token_address': token_address,
            'signal_data': signal_data,
            'directional_assessment': self._get_default_probability_assessment(ProbabilityType.DIRECTIONAL_MOVE).__dict__,
            'volatility_assessment': None,
            'combined_probability_edge': {
                'overall_edge': 0.0,
                'overall_confidence': 0.1,
                'edge_significance': 0.0,
                'edge_strength': 'negligible'
            },
            'opportunity_analysis': {
                'opportunity_score': 0.0,
                'opportunity_quality': 'poor',
                'execution_feasibility': False
            },
            'trading_recommendations': {
                'trade_recommendation': 'HOLD',
                'confidence_level': 'LOW',
                'position_size_multiplier': 0.5,
                'reasoning': ['Analysis failed']
            },
            'comparison_timestamp': datetime.now().isoformat()
        }

    def _get_default_probability_assessment(self, probability_type: ProbabilityType) -> ProbabilityAssessment:
        """Return default probability assessment when calculation fails"""
        return ProbabilityAssessment(
            probability_type=probability_type,
            market_implied_probability=0.5,
            ai_predicted_probability=0.5,
            probability_edge=0.0,
            confidence_level=0.1,
            supporting_evidence={'error': 'calculation_failed'},
            assessment_timestamp=datetime.now().isoformat()
        )


# Global instance
probability_engine = ProbabilityEngine()
