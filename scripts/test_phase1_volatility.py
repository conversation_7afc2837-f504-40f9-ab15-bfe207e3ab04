#!/usr/bin/env python3
"""
Test Phase 1: Volatility Intelligence Enhancement
Comprehensive testing of volatility analyzer, inefficiency detector, and enhanced position sizing
"""

import asyncio
import sys
import os
from datetime import datetime

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.market_intelligence.volatility_analyzer import volatility_analyzer
from src.market_intelligence.inefficiency_detector import inefficiency_detector
from src.paper_trading.portfolio_manager import PortfolioManager
from src.data_collectors.simple_asset_provider import simple_asset_provider
from config.settings import config


async def test_volatility_analyzer():
    """Test the volatility analyzer with real market data"""
    print("\n🔬 Testing Volatility Analyzer")
    print("-" * 50)
    
    try:
        # Get a sample asset
        top_assets = await simple_asset_provider.get_top_pairs(5)
        if not top_assets:
            print("❌ No assets available for testing")
            return False
        
        test_asset = top_assets[0]
        token_address = test_asset.get('token0', {}).get('id')
        symbol = test_asset.get('token0', {}).get('symbol', 'UNKNOWN')
        
        if not token_address:
            print("❌ No token address available")
            return False
        
        print(f"Testing with asset: {symbol} ({token_address[:10]}...)")
        
        # Test volatility analysis
        volatility_analysis = await volatility_analyzer.analyze_asset_volatility(token_address, "USDC")
        
        print(f"✅ Volatility analysis completed")
        print(f"   Expected daily move: {volatility_analysis.get('expected_daily_move_pct', 0):.2f}%")
        print(f"   Volatility regime: {volatility_analysis.get('volatility_regime', 'unknown')}")
        print(f"   Analysis quality: {volatility_analysis.get('analysis_quality', {}).get('quality_level', 'unknown')}")
        
        # Test volatility windows
        windows = volatility_analysis.get('volatility_windows', {})
        for window, data in windows.items():
            if isinstance(data, dict):
                annualized_vol = data.get('annualized_volatility', 0)
                data_points = data.get('data_points', 0)
                print(f"   {window}: {annualized_vol:.1f}% annualized ({data_points} data points)")
        
        # Test market expectations
        market_expectations = volatility_analysis.get('market_expectations', {})
        efficiency = market_expectations.get('efficiency_assessment', 'unknown')
        print(f"   Market efficiency: {efficiency}")
        
        return True
        
    except Exception as e:
        print(f"❌ Volatility analyzer test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_inefficiency_detector():
    """Test the market inefficiency detector"""
    print("\n🎯 Testing Market Inefficiency Detector")
    print("-" * 50)
    
    try:
        # Get a sample asset
        top_assets = await simple_asset_provider.get_top_pairs(5)
        if not top_assets:
            print("❌ No assets available for testing")
            return False
        
        test_asset = top_assets[0]
        token_address = test_asset.get('token0', {}).get('id')
        symbol = test_asset.get('token0', {}).get('symbol', 'UNKNOWN')
        
        if not token_address:
            print("❌ No token address available")
            return False
        
        print(f"Testing with asset: {symbol} ({token_address[:10]}...)")
        
        # Test inefficiency detection
        inefficiency_analysis = await inefficiency_detector.detect_market_inefficiencies(
            token_address, symbol, "USDC"
        )
        
        print(f"✅ Inefficiency detection completed")
        print(f"   Inefficiency score: {inefficiency_analysis.get('inefficiency_score', 0):.2f}")
        
        # Test individual analyses
        supply_demand = inefficiency_analysis.get('supply_demand_analysis', {})
        if supply_demand.get('imbalance_detected'):
            print(f"   ⚠️  Supply/demand imbalance detected")
            print(f"      Top source concentration: {supply_demand.get('top_source_concentration', 0):.1%}")
        
        volume_liquidity = inefficiency_analysis.get('volume_liquidity_analysis', {})
        if volume_liquidity.get('anomaly_detected'):
            print(f"   ⚠️  Volume/liquidity anomaly detected")
            print(f"      Volume/liquidity ratio: {volume_liquidity.get('volume_liquidity_ratio', 0):.2f}")
        
        sentiment = inefficiency_analysis.get('sentiment_analysis', {})
        if sentiment.get('sentiment_spike_detected'):
            print(f"   ⚠️  Sentiment spike detected")
        
        arbitrage = inefficiency_analysis.get('arbitrage_analysis', {})
        if arbitrage.get('arbitrage_opportunity'):
            print(f"   💰 Arbitrage opportunity detected")
            print(f"      Net profit potential: {arbitrage.get('net_arbitrage_percentage', 0):.2%}")
        
        # Test trading opportunities
        opportunities = inefficiency_analysis.get('trading_opportunities', [])
        print(f"   Trading opportunities found: {len(opportunities)}")
        for opp in opportunities:
            print(f"      - {opp.get('type', 'unknown')}: {opp.get('description', 'no description')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Inefficiency detector test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_enhanced_position_sizing():
    """Test the enhanced volatility-based position sizing"""
    print("\n💰 Testing Enhanced Position Sizing")
    print("-" * 50)
    
    try:
        # Create portfolio manager instance
        portfolio_manager = PortfolioManager(config)
        
        # Get a sample asset
        top_assets = await simple_asset_provider.get_top_pairs(3)
        if not top_assets:
            print("❌ No assets available for testing")
            return False
        
        # Create mock signals for testing
        test_signals = []
        for i, asset in enumerate(top_assets[:2]):
            symbol = asset.get('token0', {}).get('symbol', f'TEST{i}')
            address = asset.get('token0', {}).get('id', f'0x{i:040x}')
            
            signal = {
                'asset': f'{symbol}/USDC',
                'signal_direction': 'BUY',
                'entry_price': 1.0,
                'confidence': 0.8,
                'signal_strength': 0.7,
                'timestamp': datetime.now().isoformat()
            }
            test_signals.append((signal, address))
        
        print(f"Testing position sizing with {len(test_signals)} mock signals")
        
        # Test position sizing for each signal
        for i, (signal, token_address) in enumerate(test_signals):
            print(f"\n   Signal {i+1}: {signal['asset']}")
            
            # Test the enhanced position sizing
            position_size = await portfolio_manager._calculate_position_size(signal)
            
            print(f"   ✅ Position size calculated: ${position_size:.2f}")
            print(f"      Base balance: ${portfolio_manager.current_balance:.2f}")
            print(f"      Position as % of balance: {position_size/portfolio_manager.current_balance:.1%}")
            
            # Test fallback sizing
            fallback_size = await portfolio_manager._fallback_position_size(signal)
            print(f"      Fallback size: ${fallback_size:.2f}")
            
            # Test regime multiplier
            regime_multiplier = portfolio_manager._get_regime_multiplier('normal_volatility_stable')
            print(f"      Normal regime multiplier: {regime_multiplier}")
            
            high_vol_multiplier = portfolio_manager._get_regime_multiplier('high_volatility_expanding')
            print(f"      High vol expanding multiplier: {high_vol_multiplier}")
        
        return True
        
    except Exception as e:
        print(f"❌ Enhanced position sizing test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_integration():
    """Test integration between all Phase 1 components"""
    print("\n🔗 Testing Phase 1 Integration")
    print("-" * 50)
    
    try:
        # Get sample asset
        top_assets = await simple_asset_provider.get_top_pairs(1)
        if not top_assets:
            print("❌ No assets available for integration test")
            return False
        
        test_asset = top_assets[0]
        token_address = test_asset.get('token0', {}).get('id')
        symbol = test_asset.get('token0', {}).get('symbol', 'UNKNOWN')
        
        print(f"Integration test with: {symbol}")
        
        # Run volatility analysis
        print("   Running volatility analysis...")
        volatility_result = await volatility_analyzer.analyze_asset_volatility(token_address, "USDC")
        vol_reliable = volatility_result.get('analysis_quality', {}).get('reliable_for_trading', False)
        expected_move = volatility_result.get('expected_daily_move_pct', 0)
        
        # Run inefficiency detection
        print("   Running inefficiency detection...")
        inefficiency_result = await inefficiency_detector.detect_market_inefficiencies(
            token_address, symbol, "USDC"
        )
        inefficiency_score = inefficiency_result.get('inefficiency_score', 0)
        opportunities = len(inefficiency_result.get('trading_opportunities', []))
        
        # Test position sizing with results
        print("   Testing integrated position sizing...")
        portfolio_manager = PortfolioManager(config)
        
        signal = {
            'asset': f'{symbol}/USDC',
            'signal_direction': 'BUY',
            'entry_price': 1.0,
            'confidence': 0.8,
            'signal_strength': 0.7
        }
        
        position_size = await portfolio_manager._calculate_position_size(signal)
        
        print(f"   ✅ Integration test completed")
        print(f"      Volatility reliable: {vol_reliable}")
        print(f"      Expected daily move: {expected_move:.2f}%")
        print(f"      Inefficiency score: {inefficiency_score:.2f}")
        print(f"      Trading opportunities: {opportunities}")
        print(f"      Final position size: ${position_size:.2f}")
        
        # Assess overall system health
        system_health = {
            'volatility_analysis': vol_reliable,
            'inefficiency_detection': inefficiency_score > 0,
            'position_sizing': position_size > 0,
            'data_availability': len(top_assets) > 0
        }
        
        healthy_components = sum(system_health.values())
        total_components = len(system_health)
        
        print(f"   System health: {healthy_components}/{total_components} components operational")
        
        return healthy_components >= 3  # At least 3/4 components working
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Run all Phase 1 tests"""
    print("🚀 Phase 1: Volatility Intelligence Enhancement - Test Suite")
    print("=" * 70)
    
    test_results = []
    
    # Run individual component tests
    test_results.append(await test_volatility_analyzer())
    test_results.append(await test_inefficiency_detector())
    test_results.append(await test_enhanced_position_sizing())
    test_results.append(await test_integration())
    
    # Summary
    print("\n📊 Test Results Summary")
    print("-" * 30)
    
    test_names = [
        "Volatility Analyzer",
        "Inefficiency Detector", 
        "Enhanced Position Sizing",
        "Integration Test"
    ]
    
    passed_tests = 0
    for i, (name, result) in enumerate(zip(test_names, test_results)):
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{i+1}. {name}: {status}")
        if result:
            passed_tests += 1
    
    print(f"\nOverall: {passed_tests}/{len(test_results)} tests passed")
    
    if passed_tests == len(test_results):
        print("🎉 Phase 1 implementation is fully operational!")
        return True
    elif passed_tests >= len(test_results) * 0.75:
        print("⚠️  Phase 1 implementation is mostly operational with some issues")
        return True
    else:
        print("❌ Phase 1 implementation has significant issues")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
