#!/usr/bin/env python3
"""
Simple Phase 3 Test - Tests core logic without external dependencies
"""

import sys
import os
import numpy as np
from datetime import datetime

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_advanced_volume_analysis():
    """Test advanced volume analysis logic"""
    print("Testing Advanced Volume Analysis...")
    
    try:
        # Import the class directly to avoid dependency issues
        from src.ml_models.feature_engineering import AdvancedVolumeAnalysis
        
        # Create test data
        n_points = 50
        prices = np.random.normal(100, 5, n_points)
        volumes = np.random.exponential(1000, n_points)
        timestamps = np.arange(n_points)
        
        analyzer = AdvancedVolumeAnalysis()
        
        # Test enhanced volume profile
        print("  Testing enhanced volume profile...")
        profile_features = analyzer.enhanced_volume_profile(prices, volumes, timestamps)
        
        expected_features = ['volume_profile_strength', 'poc_distance', 'value_area_high', 'value_area_low']
        for feature in expected_features:
            if feature not in profile_features:
                print(f"    ❌ Missing feature: {feature}")
                return False
            if len(profile_features[feature]) != n_points:
                print(f"    ❌ Wrong feature length: {feature}")
                return False
        
        print(f"    ✅ Enhanced volume profile: {len(profile_features)} features")
        
        # Test institutional vs retail signatures
        print("  Testing institutional vs retail signatures...")
        signatures = analyzer.institutional_vs_retail_signatures(volumes, prices, timestamps)
        
        expected_sigs = ['institutional_volume_ratio', 'large_block_frequency', 'volume_clustering']
        for sig in expected_sigs:
            if sig not in signatures:
                print(f"    ❌ Missing signature: {sig}")
                return False
        
        print(f"    ✅ Institutional signatures: {len(signatures)} features")
        
        # Test unusual volume patterns
        print("  Testing unusual volume patterns...")
        patterns = analyzer.unusual_volume_patterns(volumes, prices)
        
        if 'price_volume_divergence' not in patterns:
            print("    ❌ Missing divergence feature")
            return False
        
        surge_features = [f for f in patterns.keys() if 'volume_surge' in f]
        if not surge_features:
            print("    ❌ Missing volume surge features")
            return False
        
        print(f"    ✅ Unusual patterns: {len(patterns)} features")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Error: {e}")
        return False

def test_liquidity_regime_logic():
    """Test liquidity regime classification logic"""
    print("Testing Liquidity Regime Classification...")
    
    try:
        from src.market_intelligence.liquidity_analyzer import LiquidityRegime
        
        # Test enum values
        regimes = [regime.value for regime in LiquidityRegime]
        expected_regimes = ['abundant', 'normal', 'constrained', 'fragmented', 'concentrated', 'migrating']
        
        for regime in expected_regimes:
            if regime not in regimes:
                print(f"    ❌ Missing regime: {regime}")
                return False
        
        print(f"    ✅ All regime types available: {len(regimes)} regimes")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Error: {e}")
        return False

def test_feature_engineering_structure():
    """Test feature engineering structure"""
    print("Testing Feature Engineering Structure...")
    
    try:
        from src.ml_models.feature_engineering import FeatureEngineer, AdvancedVolumeAnalysis
        
        # Test that FeatureEngineer has advanced volume analyzer
        engineer = FeatureEngineer()
        
        if not hasattr(engineer, 'advanced_volume'):
            print("    ❌ Missing advanced_volume attribute")
            return False
        
        if not isinstance(engineer.advanced_volume, AdvancedVolumeAnalysis):
            print("    ❌ Wrong advanced_volume type")
            return False
        
        print("    ✅ FeatureEngineer has AdvancedVolumeAnalysis")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Error: {e}")
        return False

def test_data_structures():
    """Test data structure definitions"""
    print("Testing Data Structures...")
    
    try:
        from src.market_intelligence.liquidity_analyzer import (
            LiquiditySnapshot, LiquidityMigrationPattern, LiquidityGap
        )
        
        # Test that dataclasses are properly defined
        snapshot_fields = ['timestamp', 'token_address', 'base_token', 'total_liquidity_usd']
        for field in snapshot_fields:
            if not hasattr(LiquiditySnapshot, '__annotations__'):
                print("    ❌ LiquiditySnapshot not properly defined")
                return False
            if field not in LiquiditySnapshot.__annotations__:
                print(f"    ❌ Missing field in LiquiditySnapshot: {field}")
                return False
        
        print("    ✅ LiquiditySnapshot structure valid")
        
        # Test LiquidityGap
        gap_fields = ['token_address', 'gap_type', 'price_impact_estimate']
        for field in gap_fields:
            if field not in LiquidityGap.__annotations__:
                print(f"    ❌ Missing field in LiquidityGap: {field}")
                return False
        
        print("    ✅ LiquidityGap structure valid")
        
        return True
        
    except Exception as e:
        print(f"    ❌ Error: {e}")
        return False

def main():
    """Run simple Phase 3 tests"""
    print("="*60)
    print("PHASE 3 SIMPLE TEST SUITE")
    print("="*60)
    
    tests = [
        test_advanced_volume_analysis,
        test_liquidity_regime_logic,
        test_feature_engineering_structure,
        test_data_structures
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
                print(f"✅ {test.__name__} PASSED")
            else:
                print(f"❌ {test.__name__} FAILED")
        except Exception as e:
            print(f"❌ {test.__name__} ERROR: {e}")
    
    print("\n" + "="*60)
    print(f"RESULTS: {passed}/{total} tests passed ({(passed/total)*100:.1f}%)")
    
    if passed == total:
        print("🎉 ALL SIMPLE TESTS PASSED!")
        print("Phase 3 core logic is working correctly!")
    else:
        print("⚠️  Some tests failed. Check implementation.")
    
    return 0 if passed == total else 1

if __name__ == "__main__":
    exit(main())
