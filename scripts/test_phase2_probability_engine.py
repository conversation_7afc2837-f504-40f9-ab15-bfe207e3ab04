#!/usr/bin/env python3
"""
Phase 2 Testing Script: Probability-Based Risk Management
Tests the probability engine and enhanced risk management system
"""

import asyncio
import logging
import sys
import os
from datetime import datetime
from typing import Dict, Any

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.market_intelligence.probability_engine import probability_engine, ProbabilityType
from src.paper_trading.risk_manager import RiskManager
from config.settings import Config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class Phase2Tester:
    """Comprehensive tester for Phase 2 probability-based risk management"""
    
    def __init__(self):
        self.config = Config()
        self.risk_manager = RiskManager(self.config)
        self.test_results = {
            'probability_engine': {},
            'enhanced_risk_management': {},
            'integration_tests': {},
            'performance_metrics': {}
        }
    
    async def run_all_tests(self):
        """Run comprehensive Phase 2 tests"""
        logger.info("🚀 Starting Phase 2 Probability Engine Tests")
        
        try:
            # Test 1: Probability Engine Core Functions
            await self.test_probability_engine_core()
            
            # Test 2: Enhanced Risk Management
            await self.test_enhanced_risk_management()
            
            # Test 3: Integration Tests
            await self.test_probability_risk_integration()
            
            # Test 4: Performance Tests
            await self.test_performance_metrics()
            
            # Generate summary
            self.generate_test_summary()
            
        except Exception as e:
            logger.error(f"Phase 2 testing failed: {e}")
            raise
    
    async def test_probability_engine_core(self):
        """Test core probability engine functionality"""
        logger.info("📊 Testing Probability Engine Core Functions")
        
        # Test data
        test_token_address = "******************************************"  # WMATIC
        test_signal = {
            'token_address': test_token_address,
            'signal_direction': 'BUY',
            'predicted_change': 0.05,  # 5% expected move
            'confidence': 0.75,
            'signal_strength': 0.8,
            'timeframe_hours': 24
        }
        
        results = {}
        
        try:
            # Test directional probability assessment
            logger.info("Testing directional probability assessment...")
            directional_assessment = await probability_engine.assess_directional_probability(
                test_token_address, 'BUY', 0.05, 24
            )
            
            results['directional_assessment'] = {
                'success': True,
                'market_implied_prob': directional_assessment.market_implied_probability,
                'ai_predicted_prob': directional_assessment.ai_predicted_probability,
                'probability_edge': directional_assessment.probability_edge,
                'confidence_level': directional_assessment.confidence_level
            }
            logger.info(f"✅ Directional assessment: Edge={directional_assessment.probability_edge:.3f}, Confidence={directional_assessment.confidence_level:.2f}")
            
        except Exception as e:
            results['directional_assessment'] = {'success': False, 'error': str(e)}
            logger.error(f"❌ Directional assessment failed: {e}")
        
        try:
            # Test volatility expansion probability
            logger.info("Testing volatility expansion probability...")
            volatility_assessment = await probability_engine.assess_volatility_expansion_probability(
                test_token_address, 0.02, 0.04, 24
            )
            
            results['volatility_assessment'] = {
                'success': True,
                'market_implied_prob': volatility_assessment.market_implied_probability,
                'ai_predicted_prob': volatility_assessment.ai_predicted_probability,
                'probability_edge': volatility_assessment.probability_edge,
                'confidence_level': volatility_assessment.confidence_level
            }
            logger.info(f"✅ Volatility assessment: Edge={volatility_assessment.probability_edge:.3f}, Confidence={volatility_assessment.confidence_level:.2f}")
            
        except Exception as e:
            results['volatility_assessment'] = {'success': False, 'error': str(e)}
            logger.error(f"❌ Volatility assessment failed: {e}")
        
        try:
            # Test market vs AI probability comparison
            logger.info("Testing market vs AI probability comparison...")
            comparison_result = await probability_engine.compare_market_vs_ai_probabilities(
                test_token_address, test_signal
            )
            
            combined_edge = comparison_result.get('combined_probability_edge', {})
            results['probability_comparison'] = {
                'success': True,
                'overall_edge': combined_edge.get('overall_edge', 0),
                'edge_strength': combined_edge.get('edge_strength', 'unknown'),
                'opportunity_score': comparison_result.get('opportunity_analysis', {}).get('opportunity_score', 0),
                'trade_recommendation': comparison_result.get('trading_recommendations', {}).get('trade_recommendation', 'HOLD')
            }
            logger.info(f"✅ Probability comparison: Edge={combined_edge.get('overall_edge', 0):.3f}, Strength={combined_edge.get('edge_strength', 'unknown')}")
            
        except Exception as e:
            results['probability_comparison'] = {'success': False, 'error': str(e)}
            logger.error(f"❌ Probability comparison failed: {e}")
        
        try:
            # Test probability model calibration
            logger.info("Testing probability model calibration...")
            calibration_result = await probability_engine.calibrate_probability_models(
                test_token_address, calibration_days=30
            )
            
            results['model_calibration'] = {
                'success': True,
                'directional_accuracy': calibration_result.get('directional_model', {}).get('overall_accuracy', 0),
                'volatility_accuracy': calibration_result.get('volatility_model', {}).get('overall_accuracy', 0),
                'overall_reliability': calibration_result.get('overall_reliability', {}).get('overall_score', 0),
                'sample_count': calibration_result.get('sample_count', 0)
            }
            logger.info(f"✅ Model calibration: Reliability={calibration_result.get('overall_reliability', {}).get('overall_score', 0):.2f}")
            
        except Exception as e:
            results['model_calibration'] = {'success': False, 'error': str(e)}
            logger.error(f"❌ Model calibration failed: {e}")
        
        self.test_results['probability_engine'] = results
    
    async def test_enhanced_risk_management(self):
        """Test enhanced risk management with probability integration"""
        logger.info("🛡️ Testing Enhanced Risk Management")
        
        # Test signal
        test_signal = {
            'token_address': "******************************************",
            'asset': 'WMATIC/USDC',
            'signal_direction': 'BUY',
            'predicted_change': 0.03,
            'confidence': 0.7,
            'signal_strength': 0.75,
            'entry_price': 0.85,
            'timestamp': datetime.now().isoformat()
        }
        
        # Test portfolio state
        portfolio_state = {
            'positions': {
                'position_details': [
                    {'asset': 'WETH/USDC', 'token_address': '******************************************'},
                    {'asset': 'USDC/USDT', 'token_address': '******************************************'}
                ]
            },
            'risk_metrics': {
                'current_exposure_pct': 45
            }
        }
        
        # Test market conditions
        market_conditions = {
            'volatility': 0.04,
            'volume_24h': 2500000,
            'price_change_24h': 0.02,
            'liquidity_score': 0.8
        }
        
        results = {}
        
        try:
            # Test enhanced risk assessment
            logger.info("Testing enhanced risk assessment...")
            risk_assessment = await self.risk_manager.assess_trade_risk(
                test_signal, portfolio_state, market_conditions
            )
            
            results['enhanced_risk_assessment'] = {
                'success': True,
                'risk_level': risk_assessment.risk_level.value,
                'risk_score': risk_assessment.risk_score,
                'position_multiplier': risk_assessment.position_size_multiplier,
                'warnings_count': len(risk_assessment.warnings),
                'has_probability_reasoning': 'probability' in risk_assessment.reasoning.lower()
            }
            logger.info(f"✅ Enhanced risk assessment: Level={risk_assessment.risk_level.value}, Score={risk_assessment.risk_score:.1f}, Multiplier={risk_assessment.position_size_multiplier:.2f}")
            
        except Exception as e:
            results['enhanced_risk_assessment'] = {'success': False, 'error': str(e)}
            logger.error(f"❌ Enhanced risk assessment failed: {e}")
        
        self.test_results['enhanced_risk_management'] = results
    
    async def test_probability_risk_integration(self):
        """Test integration between probability engine and risk management"""
        logger.info("🔗 Testing Probability-Risk Integration")
        
        results = {}
        
        # Test various edge scenarios
        test_scenarios = [
            {
                'name': 'strong_positive_edge',
                'signal': {
                    'token_address': "******************************************",
                    'signal_direction': 'BUY',
                    'predicted_change': 0.08,
                    'confidence': 0.9,
                    'signal_strength': 0.85
                }
            },
            {
                'name': 'weak_negative_edge',
                'signal': {
                    'token_address': "******************************************",
                    'signal_direction': 'SELL',
                    'predicted_change': -0.02,
                    'confidence': 0.4,
                    'signal_strength': 0.3
                }
            }
        ]
        
        for scenario in test_scenarios:
            try:
                logger.info(f"Testing scenario: {scenario['name']}")
                
                # Get probability assessment
                comparison_result = await probability_engine.compare_market_vs_ai_probabilities(
                    scenario['signal']['token_address'], scenario['signal']
                )
                
                # Test risk assessment with this probability data
                portfolio_state = {'positions': {'position_details': []}, 'risk_metrics': {'current_exposure_pct': 20}}
                market_conditions = {'volatility': 0.03, 'volume_24h': 1000000, 'price_change_24h': 0.01, 'liquidity_score': 0.7}
                
                risk_assessment = await self.risk_manager.assess_trade_risk(
                    scenario['signal'], portfolio_state, market_conditions
                )
                
                results[scenario['name']] = {
                    'success': True,
                    'probability_edge': comparison_result.get('combined_probability_edge', {}).get('overall_edge', 0),
                    'risk_score': risk_assessment.risk_score,
                    'position_multiplier': risk_assessment.position_size_multiplier,
                    'trade_recommendation': comparison_result.get('trading_recommendations', {}).get('trade_recommendation', 'HOLD')
                }
                
                logger.info(f"✅ {scenario['name']}: Edge={results[scenario['name']]['probability_edge']:.3f}, Risk={results[scenario['name']]['risk_score']:.1f}")
                
            except Exception as e:
                results[scenario['name']] = {'success': False, 'error': str(e)}
                logger.error(f"❌ {scenario['name']} failed: {e}")
        
        self.test_results['integration_tests'] = results
    
    async def test_performance_metrics(self):
        """Test performance of Phase 2 components"""
        logger.info("⚡ Testing Performance Metrics")
        
        import time
        results = {}
        
        try:
            # Test probability engine performance
            start_time = time.time()
            
            for i in range(5):  # Run 5 iterations
                await probability_engine.assess_directional_probability(
                    "******************************************", 'BUY', 0.05, 24
                )
            
            avg_time = (time.time() - start_time) / 5
            is_acceptable = avg_time < 5.0  # Should be under 5 seconds
            results['probability_engine_performance'] = {
                'success': is_acceptable,
                'avg_response_time_seconds': avg_time,
                'acceptable': is_acceptable
            }

            if is_acceptable:
                logger.info(f"✅ Probability engine avg response time: {avg_time:.2f}s")
            else:
                logger.error(f"❌ Probability engine too slow: {avg_time:.2f}s (should be < 5.0s)")
            
        except Exception as e:
            results['probability_engine_performance'] = {'success': False, 'error': str(e)}
            logger.error(f"❌ Performance test failed: {e}")
        
        self.test_results['performance_metrics'] = results
    
    def generate_test_summary(self):
        """Generate comprehensive test summary"""
        logger.info("📋 Generating Phase 2 Test Summary")
        
        total_tests = 0
        passed_tests = 0
        
        for category, tests in self.test_results.items():
            for test_name, result in tests.items():
                total_tests += 1
                if result.get('success', False):
                    passed_tests += 1
        
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        print("\n" + "="*80)
        print("🎯 PHASE 2 PROBABILITY ENGINE TEST SUMMARY")
        print("="*80)
        print(f"📊 Overall Results: {passed_tests}/{total_tests} tests passed ({success_rate:.1f}%)")
        print()
        
        for category, tests in self.test_results.items():
            print(f"📁 {category.upper().replace('_', ' ')}:")
            for test_name, result in tests.items():
                status = "✅ PASS" if result.get('success', False) else "❌ FAIL"
                print(f"   {status} {test_name}")
                if not result.get('success', False) and 'error' in result:
                    print(f"      Error: {result['error']}")
            print()
        
        print("🎉 Phase 2 Implementation Status:")
        if success_rate >= 75:
            print("   ✅ OPERATIONAL - Probability-based risk management is working!")
        elif success_rate >= 50:
            print("   ⚠️  PARTIAL - Some components working, needs attention")
        else:
            print("   ❌ NEEDS WORK - Major issues detected")
        
        print("="*80)


async def main():
    """Main test execution"""
    tester = Phase2Tester()
    await tester.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
